"""
SQLAlchemy database models for USDC-DeFi Mining Platform
"""

from sqlalchemy import (
    Column, Integer, BigInteger, String, Decimal, Boolean,
    DateTime, Date, Text, Enum, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, date
from decimal import Decimal as PyDecimal
import enum

from app.core.database import Base

class DepositStatus(str, enum.Enum):
    """Deposit status enumeration"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    FAILED = "failed"

class EarningStatus(str, enum.Enum):
    """Earning status enumeration"""
    PENDING = "pending"
    DISTRIBUTED = "distributed"
    FAILED = "failed"

class WithdrawalStatus(str, enum.Enum):
    """Withdrawal status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class WithdrawalType(str, enum.Enum):
    """Withdrawal type enumeration"""
    PRINCIPAL = "principal"
    EARNINGS = "earnings"
    BOTH = "both"

class TransactionType(str, enum.Enum):
    """Transaction type enumeration"""
    DEPOSIT = "deposit"
    WITHDRAWAL = "withdrawal"
    EARNING_DISTRIBUTION = "earning_distribution"

class TransactionStatus(str, enum.Enum):
    """Transaction status enumeration"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    FAILED = "failed"

class User(Base):
    """User model for wallet-based authentication"""
    __tablename__ = "users"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    wallet_address = Column(String(42), unique=True, nullable=False, index=True)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    last_login_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True, index=True)
    total_deposited = Column(Decimal(20, 6), default=PyDecimal('0.000000'))
    total_earned = Column(Decimal(20, 6), default=PyDecimal('0.000000'))
    
    # Relationships
    deposits = relationship("Deposit", back_populates="user", cascade="all, delete-orphan")
    earnings = relationship("Earning", back_populates="user", cascade="all, delete-orphan")
    withdrawals = relationship("Withdrawal", back_populates="user", cascade="all, delete-orphan")
    transactions = relationship("Transaction", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, wallet={self.wallet_address[:10]}...)>"

class InterestTier(Base):
    """Interest rate tier configuration"""
    __tablename__ = "interest_tiers"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    tier_name = Column(String(50), nullable=False)
    min_amount = Column(Decimal(20, 6), nullable=False)
    max_amount = Column(Decimal(20, 6), nullable=True)
    min_rate = Column(Decimal(5, 4), nullable=False)
    max_rate = Column(Decimal(5, 4), nullable=False)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # Relationships
    deposits = relationship("Deposit", back_populates="interest_tier")
    
    # Indexes
    __table_args__ = (
        Index('idx_amount_range', 'min_amount', 'max_amount'),
    )
    
    def __repr__(self):
        return f"<InterestTier(id={self.id}, name={self.tier_name}, rate={self.min_rate}-{self.max_rate})>"

class Deposit(Base):
    """User deposit records"""
    __tablename__ = "deposits"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    amount = Column(Decimal(20, 6), nullable=False)
    interest_tier_id = Column(Integer, ForeignKey("interest_tiers.id"), nullable=False)
    interest_rate = Column(Decimal(5, 4), nullable=False)
    transaction_hash = Column(String(66), nullable=True, index=True)
    status = Column(Enum(DepositStatus), default=DepositStatus.PENDING, index=True)
    created_at = Column(DateTime, default=func.current_timestamp(), index=True)
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # Relationships
    user = relationship("User", back_populates="deposits")
    interest_tier = relationship("InterestTier", back_populates="deposits")
    earnings = relationship("Earning", back_populates="deposit", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Deposit(id={self.id}, user_id={self.user_id}, amount={self.amount})>"

class Earning(Base):
    """Daily earnings records"""
    __tablename__ = "earnings"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    deposit_id = Column(BigInteger, ForeignKey("deposits.id", ondelete="CASCADE"), nullable=False, index=True)
    amount = Column(Decimal(20, 6), nullable=False)
    earning_date = Column(Date, nullable=False, index=True)
    interest_rate = Column(Decimal(5, 4), nullable=False)
    status = Column(Enum(EarningStatus), default=EarningStatus.PENDING, index=True)
    transaction_hash = Column(String(66), nullable=True)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # Relationships
    user = relationship("User", back_populates="earnings")
    deposit = relationship("Deposit", back_populates="earnings")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'deposit_id', 'earning_date', name='unique_daily_earning'),
    )
    
    def __repr__(self):
        return f"<Earning(id={self.id}, user_id={self.user_id}, amount={self.amount}, date={self.earning_date})>"

class Withdrawal(Base):
    """Withdrawal requests and records"""
    __tablename__ = "withdrawals"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    withdrawal_type = Column(Enum(WithdrawalType), nullable=False)
    amount = Column(Decimal(20, 6), nullable=False)
    status = Column(Enum(WithdrawalStatus), default=WithdrawalStatus.PENDING, index=True)
    transaction_hash = Column(String(66), nullable=True)
    requested_at = Column(DateTime, default=func.current_timestamp(), index=True)
    processed_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="withdrawals")
    
    def __repr__(self):
        return f"<Withdrawal(id={self.id}, user_id={self.user_id}, amount={self.amount}, type={self.withdrawal_type})>"

class Transaction(Base):
    """Blockchain transaction records"""
    __tablename__ = "transactions"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    transaction_hash = Column(String(66), unique=True, nullable=False, index=True)
    transaction_type = Column(Enum(TransactionType), nullable=False, index=True)
    amount = Column(Decimal(20, 6), nullable=False)
    gas_fee = Column(Decimal(20, 6), default=PyDecimal('0.000000'))
    block_number = Column(BigInteger, nullable=True, index=True)
    block_timestamp = Column(DateTime, nullable=True)
    status = Column(Enum(TransactionStatus), default=TransactionStatus.PENDING, index=True)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    # Relationships
    user = relationship("User", back_populates="transactions")
    
    def __repr__(self):
        return f"<Transaction(id={self.id}, hash={self.transaction_hash[:10]}..., type={self.transaction_type})>"

class DemoDeposit(Base):
    """Mock deposit data for homepage display"""
    __tablename__ = "demo_deposits"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    wallet_address = Column(String(42), nullable=False)
    amount = Column(Decimal(20, 6), nullable=False)
    display_time = Column(String(20), nullable=False)
    created_at = Column(DateTime, default=func.current_timestamp(), index=True)
    is_active = Column(Boolean, default=True, index=True)
    
    def __repr__(self):
        return f"<DemoDeposit(id={self.id}, wallet={self.wallet_address}, amount={self.amount})>"

class SystemConfig(Base):
    """System configuration parameters"""
    __tablename__ = "system_config"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    config_key = Column(String(100), unique=True, nullable=False, index=True)
    config_value = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime, default=func.current_timestamp())
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    def __repr__(self):
        return f"<SystemConfig(key={self.config_key}, value={self.config_value[:50]}...)>"
