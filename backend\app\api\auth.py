"""
Authentication routes for wallet-based login
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Optional

from app.core.database import get_db
from app.core.config import settings
from app.db.models import User
from app.db.schemas import WalletSignatureRequest, AuthResponse, UserResponse, APIResponse
from app.services.auth import AuthService
from app.services.user import UserService
from app.utils.security import create_access_token
from app.utils.web3 import Web3Service

router = APIRouter()

@router.post("/wallet-login", response_model=AuthResponse, summary="Wallet-based login")
async def wallet_login(
    auth_request: WalletSignatureRequest,
    db: Session = Depends(get_db)
):
    """
    Authenticate user with wallet signature
    
    This endpoint verifies the wallet signature and creates/updates user account.
    If the user doesn't exist, a new account is automatically created.
    """
    try:
        # Verify wallet signature
        auth_service = AuthService()
        is_valid = await auth_service.verify_wallet_signature(
            wallet_address=auth_request.wallet_address,
            signature=auth_request.signature,
            message=auth_request.message,
            timestamp=auth_request.timestamp
        )
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid wallet signature"
            )
        
        # Get or create user
        user_service = UserService(db)
        user = await user_service.get_or_create_user(auth_request.wallet_address)
        
        # Update last login time
        await user_service.update_last_login(user.id)
        
        # Create access token
        access_token = create_access_token(
            data={"sub": str(user.id), "wallet": user.wallet_address}
        )
        
        return AuthResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserResponse.from_orm(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Authentication failed: {str(e)}"
        )

@router.post("/generate-message", response_model=dict, summary="Generate authentication message")
async def generate_auth_message(wallet_address: str):
    """
    Generate a message for wallet signature authentication
    
    This message should be signed by the user's wallet to prove ownership.
    """
    try:
        auth_service = AuthService()
        message_data = await auth_service.generate_auth_message(wallet_address)
        
        return {
            "success": True,
            "message": "Authentication message generated successfully",
            "data": message_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate authentication message: {str(e)}"
        )

@router.post("/verify-signature", response_model=APIResponse, summary="Verify wallet signature")
async def verify_signature(auth_request: WalletSignatureRequest):
    """
    Verify wallet signature without logging in
    
    This endpoint can be used to test signature verification.
    """
    try:
        auth_service = AuthService()
        is_valid = await auth_service.verify_wallet_signature(
            wallet_address=auth_request.wallet_address,
            signature=auth_request.signature,
            message=auth_request.message,
            timestamp=auth_request.timestamp
        )
        
        return APIResponse(
            success=is_valid,
            message="Signature verified successfully" if is_valid else "Invalid signature",
            data={"valid": is_valid}
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Signature verification failed: {str(e)}"
        )

@router.get("/me", response_model=UserResponse, summary="Get current user")
async def get_current_user(
    current_user: User = Depends(AuthService.get_current_user)
):
    """
    Get current authenticated user information
    """
    return UserResponse.from_orm(current_user)

@router.post("/refresh", response_model=AuthResponse, summary="Refresh access token")
async def refresh_token(
    current_user: User = Depends(AuthService.get_current_user),
    db: Session = Depends(get_db)
):
    """
    Refresh the access token for the current user
    """
    try:
        # Create new access token
        access_token = create_access_token(
            data={"sub": str(current_user.id), "wallet": current_user.wallet_address}
        )
        
        # Update last login time
        user_service = UserService(db)
        await user_service.update_last_login(current_user.id)
        
        return AuthResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserResponse.from_orm(current_user)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Token refresh failed: {str(e)}"
        )

@router.post("/logout", response_model=APIResponse, summary="Logout user")
async def logout(
    current_user: User = Depends(AuthService.get_current_user)
):
    """
    Logout the current user
    
    Note: Since we're using stateless JWT tokens, this endpoint mainly serves
    as a confirmation. The client should discard the token.
    """
    return APIResponse(
        success=True,
        message="Logged out successfully",
        data={"wallet_address": current_user.wallet_address}
    )
