import { getDefaultWallets, connectorsForWallets } from '@rainbow-me/rainbowkit';
import {
  trustWallet,
  coinbaseWallet,
  krakenWallet,
} from '@rainbow-me/rainbowkit/wallets';
import { configureChains, createConfig } from 'wagmi';
import { mainnet, goerli, sepolia } from 'wagmi/chains';
import { publicProvider } from 'wagmi/providers/public';
import { infuraProvider } from 'wagmi/providers/infura';
import { alchemyProvider } from 'wagmi/providers/alchemy';

// Environment variables
const projectId = process.env.REACT_APP_RAINBOWKIT_PROJECT_ID!;
const infuraKey = process.env.REACT_APP_INFURA_KEY;
const alchemyKey = process.env.REACT_APP_ALCHEMY_KEY;
const environment = process.env.REACT_APP_ENVIRONMENT || 'development';

// Configure chains based on environment
export const { chains, publicClient, webSocketPublicClient } = configureChains(
  environment === 'production' 
    ? [mainnet]
    : [mainnet, goerli, sepolia],
  [
    // Use Infura if key is available
    ...(infuraKey ? [infuraProvider({ apiKey: infuraKey })] : []),
    // Use Alchemy if key is available
    ...(alchemyKey ? [alchemyProvider({ apiKey: alchemyKey })] : []),
    // Always include public provider as fallback
    publicProvider(),
  ]
);

// Configure wallet connectors
const { wallets } = getDefaultWallets({
  appName: process.env.REACT_APP_APP_NAME || 'USDC-DeFi Mining Platform',
  projectId,
  chains,
});

// Custom wallet configuration with preferred wallets
const connectors = connectorsForWallets([
  {
    groupName: 'Recommended',
    wallets: [
      trustWallet({ projectId, chains }),
      coinbaseWallet({ appName: 'USDC-DeFi Mining Platform', chains }),
      krakenWallet({ projectId, chains }),
    ],
  },
  ...wallets,
]);

// Create wagmi config
export const wagmiConfig = createConfig({
  autoConnect: true,
  connectors,
  publicClient,
  webSocketPublicClient,
});

// Chain configuration
export const supportedChains = {
  mainnet: {
    id: 1,
    name: 'Ethereum',
    network: 'homestead',
    nativeCurrency: {
      decimals: 18,
      name: 'Ether',
      symbol: 'ETH',
    },
    rpcUrls: {
      default: {
        http: ['https://eth-mainnet.alchemyapi.io/v2/your-api-key'],
      },
      public: {
        http: ['https://eth-mainnet.alchemyapi.io/v2/your-api-key'],
      },
    },
    blockExplorers: {
      default: { name: 'Etherscan', url: 'https://etherscan.io' },
    },
    contracts: {
      usdc: {
        address: process.env.REACT_APP_USDC_CONTRACT_ADDRESS as `0x${string}`,
        abi: [], // USDC ABI would go here
      },
      platform: {
        address: process.env.REACT_APP_PLATFORM_CONTRACT_ADDRESS as `0x${string}`,
        abi: [], // Platform contract ABI would go here
      },
    },
  },
  goerli: {
    id: 5,
    name: 'Goerli',
    network: 'goerli',
    nativeCurrency: {
      decimals: 18,
      name: 'Goerli Ether',
      symbol: 'ETH',
    },
    rpcUrls: {
      default: {
        http: ['https://eth-goerli.alchemyapi.io/v2/your-api-key'],
      },
      public: {
        http: ['https://eth-goerli.alchemyapi.io/v2/your-api-key'],
      },
    },
    blockExplorers: {
      default: { name: 'Etherscan', url: 'https://goerli.etherscan.io' },
    },
    contracts: {
      usdc: {
        address: '******************************************' as `0x${string}`, // Goerli USDC
        abi: [],
      },
      platform: {
        address: process.env.REACT_APP_PLATFORM_CONTRACT_ADDRESS as `0x${string}`,
        abi: [],
      },
    },
  },
};

// Contract addresses by chain
export const getContractAddress = (chainId: number, contract: 'usdc' | 'platform'): string => {
  const chainConfig = Object.values(supportedChains).find(chain => chain.id === chainId);
  return chainConfig?.contracts[contract]?.address || '';
};

// Network utilities
export const isMainnet = (chainId: number): boolean => chainId === 1;
export const isTestnet = (chainId: number): boolean => [5, 11155111].includes(chainId);

// Gas configuration
export const gasConfig = {
  mainnet: {
    gasLimit: {
      deposit: 150000,
      withdraw: 200000,
      claim: 100000,
    },
    maxFeePerGas: '50000000000', // 50 gwei
    maxPriorityFeePerGas: '2000000000', // 2 gwei
  },
  testnet: {
    gasLimit: {
      deposit: 150000,
      withdraw: 200000,
      claim: 100000,
    },
    maxFeePerGas: '20000000000', // 20 gwei
    maxPriorityFeePerGas: '1000000000', // 1 gwei
  },
};

// Get gas configuration for current chain
export const getGasConfig = (chainId: number) => {
  return isMainnet(chainId) ? gasConfig.mainnet : gasConfig.testnet;
};

// Wallet connection utilities
export const getWalletDisplayName = (connector: any): string => {
  const walletNames: Record<string, string> = {
    'trust': 'Trust Wallet',
    'coinbase': 'Coinbase Wallet',
    'kraken': 'Kraken Wallet',
    'metamask': 'MetaMask',
    'walletconnect': 'WalletConnect',
    'injected': 'Browser Wallet',
  };

  return walletNames[connector?.id] || connector?.name || 'Unknown Wallet';
};

// Transaction utilities
export const formatTransactionHash = (hash: string): string => {
  return `${hash.slice(0, 6)}...${hash.slice(-4)}`;
};

export const getExplorerUrl = (chainId: number, hash: string, type: 'tx' | 'address' = 'tx'): string => {
  const baseUrls: Record<number, string> = {
    1: 'https://etherscan.io',
    5: 'https://goerli.etherscan.io',
    11155111: 'https://sepolia.etherscan.io',
  };

  const baseUrl = baseUrls[chainId] || baseUrls[1];
  return `${baseUrl}/${type}/${hash}`;
};

// Error handling
export const getWalletErrorMessage = (error: any): string => {
  if (error?.code === 4001) {
    return 'Transaction was rejected by user';
  }
  if (error?.code === -32603) {
    return 'Internal JSON-RPC error';
  }
  if (error?.message?.includes('insufficient funds')) {
    return 'Insufficient funds for transaction';
  }
  if (error?.message?.includes('gas')) {
    return 'Gas estimation failed. Please try again.';
  }
  
  return error?.message || 'An unknown error occurred';
};
