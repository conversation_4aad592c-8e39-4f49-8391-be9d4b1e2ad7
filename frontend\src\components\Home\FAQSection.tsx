import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { ExpandMore } from '@mui/icons-material';

const FAQSection: React.FC = () => {
  const [expanded, setExpanded] = useState<string | false>(false);

  const faqs = [
    {
      id: 'faq1',
      question: 'How does the USDC-DeFi platform work?',
      answer: 'Our platform allows you to deposit USDC and earn interest through our tiered rate system. Higher deposits unlock better rates, up to 4.1% APY.',
    },
    {
      id: 'faq2',
      question: 'Are there any fees?',
      answer: 'No! All transaction fees are sponsored by the platform. You can deposit, earn, and withdraw without paying any gas fees.',
    },
    {
      id: 'faq3',
      question: 'How often are earnings distributed?',
      answer: 'Earnings are calculated and distributed twice daily at 12:00 PM UTC and 12:00 AM UTC.',
    },
    {
      id: 'faq4',
      question: 'Can I withdraw my funds anytime?',
      answer: 'Yes, you can withdraw your principal and earnings anytime without any penalties or lock-up periods.',
    },
    {
      id: 'faq5',
      question: 'Is the platform secure?',
      answer: 'Yes, our smart contracts are built using OpenZeppelin framework and have been professionally audited for security.',
    },
  ];

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <Box sx={{ py: 8, backgroundColor: 'background.paper' }}>
      <Container maxWidth="md">
        <Typography variant="h3" textAlign="center" gutterBottom sx={{ mb: 6 }}>
          Frequently Asked Questions
        </Typography>
        {faqs.map((faq) => (
          <Accordion
            key={faq.id}
            expanded={expanded === faq.id}
            onChange={handleChange(faq.id)}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="h6">{faq.question}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body1" color="text.secondary">
                {faq.answer}
              </Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </Container>
    </Box>
  );
};

export default FAQSection;
