{"name": "usdc-defi-contracts", "version": "1.0.0", "description": "Smart contracts for USDC-DeFi Mining Platform", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "test:coverage": "hardhat coverage", "test:gas": "REPORT_GAS=true hardhat test", "deploy:local": "hardhat run scripts/deploy.js --network localhost", "deploy:goerli": "hardhat run scripts/deploy.js --network goerli", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia", "deploy:mainnet": "hardhat run scripts/deploy.js --network mainnet", "verify:goerli": "hardhat verify --network goerli", "verify:sepolia": "hardhat verify --network sepolia", "verify:mainnet": "hardhat verify --network mainnet", "node": "hardhat node", "clean": "hardhat clean", "size": "hardhat size-contracts", "flatten": "hardhat flatten", "lint": "solhint 'contracts/**/*.sol'", "lint:fix": "solhint 'contracts/**/*.sol' --fix", "format": "prettier --write 'contracts/**/*.sol'"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.2", "@nomicfoundation/hardhat-ethers": "^3.0.4", "@nomicfoundation/hardhat-network-helpers": "^1.0.9", "@nomicfoundation/hardhat-toolbox": "^3.0.0", "@nomicfoundation/hardhat-verify": "^1.1.1", "@openzeppelin/hardhat-upgrades": "^1.28.0", "@typechain/ethers-v6": "^0.4.3", "@typechain/hardhat": "^8.0.3", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.2", "chai": "^4.3.8", "dotenv": "^16.3.1", "ethers": "^6.8.1", "hardhat": "^2.17.4", "hardhat-contract-sizer": "^2.10.0", "hardhat-gas-reporter": "^1.0.9", "prettier": "^3.0.3", "prettier-plugin-solidity": "^1.1.3", "solhint": "^4.0.0", "solidity-coverage": "^0.8.5", "typechain": "^8.3.2"}, "dependencies": {"@openzeppelin/contracts": "^4.9.3", "@openzeppelin/contracts-upgradeable": "^4.9.3"}, "keywords": ["solidity", "ethereum", "smart-contracts", "defi", "usdc", "mining", "yield-farming"], "author": "USDC-DeFi Team", "license": "MIT"}