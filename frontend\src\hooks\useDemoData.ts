import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '../utils/api';

// Types
interface DemoDeposit {
  id: number;
  wallet_address: string;
  amount: string;
  amount_raw: number;
  display_time: string;
  created_at: string;
  is_active: boolean;
  activity_type: string;
  formatted_text: string;
}

interface DemoStats {
  total_deposits: number;
  active_deposits: number;
  amount_stats: {
    min_amount: number;
    max_amount: number;
    avg_amount: number;
    total_amount: number;
  };
  time_distribution: Array<{
    time: string;
    count: number;
  }>;
}

interface UseDemoDataReturn {
  demoData: DemoDeposit[] | null;
  stats: DemoStats | null;
  isLoading: boolean;
  error: string | null;
  refreshData: () => void;
  getRandomData: (count?: number) => Promise<DemoDeposit[]>;
  getRecentData: (minutes?: number) => Promise<DemoDeposit[]>;
}

export const useDemoData = (): UseDemoDataReturn => {
  const [demoData, setDemoData] = useState<DemoDeposit[] | null>(null);
  const [stats, setStats] = useState<DemoStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch demo deposits
  const fetchDemoData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.get('/demo/activity-feed?count=30');
      
      if (response.data.success) {
        setDemoData(response.data.data.activities);
      } else {
        throw new Error(response.data.message || 'Failed to fetch demo data');
      }
    } catch (err: any) {
      console.error('Error fetching demo data:', err);
      setError(err.message || 'Failed to load demo data');
      
      // Fallback to mock data if API fails
      setDemoData(getMockDemoData());
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch demo statistics
  const fetchStats = useCallback(async () => {
    try {
      const response = await apiClient.get('/demo/stats');
      
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching demo stats:', err);
    }
  }, []);

  // Get random demo data
  const getRandomData = useCallback(async (count: number = 10): Promise<DemoDeposit[]> => {
    try {
      const response = await apiClient.get(`/demo/deposits/random?count=${count}`);
      return response.data;
    } catch (err) {
      console.error('Error fetching random demo data:', err);
      return getMockDemoData().slice(0, count);
    }
  }, []);

  // Get recent demo data
  const getRecentData = useCallback(async (minutes: number = 60): Promise<DemoDeposit[]> => {
    try {
      const response = await apiClient.get(`/demo/deposits/recent?minutes=${minutes}`);
      return response.data;
    } catch (err) {
      console.error('Error fetching recent demo data:', err);
      return getMockDemoData().slice(0, 10);
    }
  }, []);

  // Refresh data
  const refreshData = useCallback(() => {
    fetchDemoData();
    fetchStats();
  }, [fetchDemoData, fetchStats]);

  // Initial data fetch
  useEffect(() => {
    fetchDemoData();
    fetchStats();
  }, [fetchDemoData, fetchStats]);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 30000);

    return () => clearInterval(interval);
  }, [refreshData]);

  return {
    demoData,
    stats,
    isLoading,
    error,
    refreshData,
    getRandomData,
    getRecentData,
  };
};

// Mock data fallback
const getMockDemoData = (): DemoDeposit[] => {
  const mockData: DemoDeposit[] = [
    {
      id: 1,
      wallet_address: '0x1a2b***3c4d',
      amount: '1,250',
      amount_raw: 1250,
      display_time: '2 minutes ago',
      created_at: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x1a2b***3c4d deposited 1,250 USDC'
    },
    {
      id: 2,
      wallet_address: '0x5e6f***7g8h',
      amount: '8,750',
      amount_raw: 8750,
      display_time: '5 minutes ago',
      created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x5e6f***7g8h deposited 8,750 USDC'
    },
    {
      id: 3,
      wallet_address: '0x9i0j***1k2l',
      amount: '25,000',
      amount_raw: 25000,
      display_time: '8 minutes ago',
      created_at: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x9i0j***1k2l deposited 25,000 USDC'
    },
    {
      id: 4,
      wallet_address: '0x3m4n***5o6p',
      amount: '150,000',
      amount_raw: 150000,
      display_time: '12 minutes ago',
      created_at: new Date(Date.now() - 12 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x3m4n***5o6p deposited 150,000 USDC'
    },
    {
      id: 5,
      wallet_address: '0x7q8r***9s0t',
      amount: '3,500',
      amount_raw: 3500,
      display_time: '15 minutes ago',
      created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x7q8r***9s0t deposited 3,500 USDC'
    },
    {
      id: 6,
      wallet_address: '0x1u2v***3w4x',
      amount: '75,000',
      amount_raw: 75000,
      display_time: '18 minutes ago',
      created_at: new Date(Date.now() - 18 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x1u2v***3w4x deposited 75,000 USDC'
    },
    {
      id: 7,
      wallet_address: '0x5y6z***7a8b',
      amount: '450,000',
      amount_raw: 450000,
      display_time: '22 minutes ago',
      created_at: new Date(Date.now() - 22 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x5y6z***7a8b deposited 450,000 USDC'
    },
    {
      id: 8,
      wallet_address: '0x9c0d***1e2f',
      amount: '12,000',
      amount_raw: 12000,
      display_time: '25 minutes ago',
      created_at: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x9c0d***1e2f deposited 12,000 USDC'
    },
    {
      id: 9,
      wallet_address: '0x3g4h***5i6j',
      amount: '2,800',
      amount_raw: 2800,
      display_time: '28 minutes ago',
      created_at: new Date(Date.now() - 28 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x3g4h***5i6j deposited 2,800 USDC'
    },
    {
      id: 10,
      wallet_address: '0x7k8l***9m0n',
      amount: '180,000',
      amount_raw: 180000,
      display_time: '32 minutes ago',
      created_at: new Date(Date.now() - 32 * 60 * 1000).toISOString(),
      is_active: true,
      activity_type: 'deposit',
      formatted_text: '0x7k8l***9m0n deposited 180,000 USDC'
    }
  ];

  // Generate more mock data
  const additionalMockData = Array.from({ length: 20 }, (_, index) => ({
    id: 11 + index,
    wallet_address: `0x${Math.random().toString(16).substr(2, 4)}***${Math.random().toString(16).substr(2, 4)}`,
    amount: (Math.floor(Math.random() * 500000) + 1000).toLocaleString(),
    amount_raw: Math.floor(Math.random() * 500000) + 1000,
    display_time: `${Math.floor(Math.random() * 120) + 30} minutes ago`,
    created_at: new Date(Date.now() - (Math.floor(Math.random() * 120) + 30) * 60 * 1000).toISOString(),
    is_active: true,
    activity_type: 'deposit',
    formatted_text: `0x${Math.random().toString(16).substr(2, 4)}***${Math.random().toString(16).substr(2, 4)} deposited ${(Math.floor(Math.random() * 500000) + 1000).toLocaleString()} USDC`
  }));

  return [...mockData, ...additionalMockData];
};
