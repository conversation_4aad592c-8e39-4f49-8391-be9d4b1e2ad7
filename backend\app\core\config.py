"""
Application configuration settings
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field, validator
from pathlib import Path

class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application settings
    APP_NAME: str = "USDC-DeFi Mining Platform"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    ENVIRONMENT: str = Field(default="production", env="ENVIRONMENT")
    
    # Server settings
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    WORKERS: int = Field(default=4, env="WORKERS")
    SERVE_STATIC: bool = Field(default=False, env="SERVE_STATIC")
    
    # Security settings
    SECRET_KEY: str = Field(env="SECRET_KEY")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    
    # CORS settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        env="ALLOWED_HOSTS"
    )
    
    # Database settings
    DB_HOST: str = Field(default="localhost", env="DB_HOST")
    DB_PORT: int = Field(default=3306, env="DB_PORT")
    DB_USER: str = Field(default="root", env="DB_USER")
    DB_PASSWORD: str = Field(default="", env="DB_PASSWORD")
    DB_NAME: str = Field(default="usdc_defi", env="DB_NAME")
    DB_CHARSET: str = Field(default="utf8mb4", env="DB_CHARSET")
    
    # Redis settings (for caching and background tasks)
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # Blockchain settings
    ETHEREUM_RPC_URL: str = Field(env="ETHEREUM_RPC_URL")
    USDC_CONTRACT_ADDRESS: str = Field(
        default="******************************************",
        env="USDC_CONTRACT_ADDRESS"
    )
    PLATFORM_CONTRACT_ADDRESS: str = Field(
        default="******************************************",
        env="PLATFORM_CONTRACT_ADDRESS"
    )
    GAS_SPONSOR_WALLET: str = Field(
        default="******************************************",
        env="GAS_SPONSOR_WALLET"
    )
    GAS_SPONSOR_PRIVATE_KEY: str = Field(env="GAS_SPONSOR_PRIVATE_KEY")
    
    # OpenZeppelin Defender settings
    DEFENDER_API_KEY: str = Field(env="DEFENDER_API_KEY")
    DEFENDER_API_SECRET: str = Field(env="DEFENDER_API_SECRET")
    DEFENDER_RELAYER_ID: str = Field(env="DEFENDER_RELAYER_ID")
    
    # RainbowKit settings
    RAINBOWKIT_PROJECT_ID: str = Field(
        default="1a54ba92caa7810745990910f7daccc4",
        env="RAINBOWKIT_PROJECT_ID"
    )
    
    # Platform settings
    MINIMUM_DEPOSIT: float = Field(default=100.0, env="MINIMUM_DEPOSIT")
    MAXIMUM_DEPOSIT: float = Field(default=10000000.0, env="MAXIMUM_DEPOSIT")
    DAILY_DISTRIBUTION_TIMES: List[str] = Field(
        default=["12:00:00", "00:00:00"],
        env="DAILY_DISTRIBUTION_TIMES"
    )
    WITHDRAWAL_FEE_RATE: float = Field(default=0.0, env="WITHDRAWAL_FEE_RATE")
    PLATFORM_FEE_RATE: float = Field(default=0.0, env="PLATFORM_FEE_RATE")
    
    # Logging settings
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    
    # Email settings (for notifications)
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USER: Optional[str] = Field(default=None, env="SMTP_USER")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SMTP_TLS: bool = Field(default=True, env="SMTP_TLS")
    
    # Monitoring and alerting
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("DAILY_DISTRIBUTION_TIMES", pre=True)
    def parse_distribution_times(cls, v):
        if isinstance(v, str):
            return [time.strip() for time in v.split(",")]
        return v
    
    @property
    def database_url(self) -> str:
        """Construct database URL for SQLAlchemy"""
        return (
            f"mysql+mysqlconnector://{self.DB_USER}:{self.DB_PASSWORD}"
            f"@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
            f"?charset={self.DB_CHARSET}"
        )
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.ENVIRONMENT.lower() == "development"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Validate required settings in production
if settings.is_production:
    required_settings = [
        "SECRET_KEY",
        "ETHEREUM_RPC_URL",
        "GAS_SPONSOR_PRIVATE_KEY",
        "DEFENDER_API_KEY",
        "DEFENDER_API_SECRET",
        "DEFENDER_RELAYER_ID"
    ]
    
    missing_settings = []
    for setting in required_settings:
        if not getattr(settings, setting, None):
            missing_settings.append(setting)
    
    if missing_settings:
        raise ValueError(
            f"Missing required environment variables for production: {', '.join(missing_settings)}"
        )
