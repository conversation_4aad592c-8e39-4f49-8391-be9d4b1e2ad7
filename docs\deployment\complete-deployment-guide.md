# USDC-DeFi Platform - Complete Deployment Guide

This comprehensive guide covers the complete deployment process for the USDC-DeFi Mining Platform, from development to production.

## 📋 Prerequisites

### System Requirements
- **Server**: 4+ CPU cores, 8GB+ RAM, 100GB+ SSD
- **Operating System**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **Domain**: Registered domain with DNS access
- **SSL Certificate**: Valid SSL certificate for HTTPS

### Required Accounts
- **Infura/Alchemy**: Ethereum RPC provider
- **OpenZeppelin Defender**: Meta-transaction relayer
- **Domain Registrar**: For DNS management
- **Email Service**: For notifications (optional)

### Development Tools
- **Git**: Version control
- **Node.js**: 16+ for frontend
- **Python**: 3.8+ for backend
- **MySQL**: 8.0+ for database
- **Docker**: For containerization (optional)

## 🚀 Deployment Overview

### Deployment Architecture
```
Internet → Cloudflare/CDN → Nginx → FastAPI Backend
                                 ↓
                              MySQL Database
                                 ↓
                           Ethereum Network
```

### Deployment Environments
1. **Development**: Local development environment
2. **Staging**: Testing environment
3. **Production**: Live production environment

## 🔧 Environment Setup

### 1. Server Preparation

#### Update System
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git unzip

# CentOS/RHEL
sudo yum update -y
sudo yum install -y curl wget git unzip
```

#### Install Required Software
```bash
# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python 3.8+
sudo apt install -y python3 python3-pip python3-venv

# Install MySQL 8.0
sudo apt install -y mysql-server mysql-client

# Install Nginx
sudo apt install -y nginx

# Install PM2
sudo npm install -g pm2
```

### 2. Database Setup

#### MySQL Configuration
```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE usdc_defi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'usdc_defi_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON usdc_defi.* TO 'usdc_defi_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### Import Database Schema
```bash
# Clone repository
git clone https://github.com/your-username/usdc-defi.git
cd usdc-defi

# Import database schema
mysql -u usdc_defi_user -p usdc_defi < database/schema.sql
mysql -u usdc_defi_user -p usdc_defi < database/migrations/001_initial_data.sql
mysql -u usdc_defi_user -p usdc_defi < database/seeds/demo_data.sql
```

### 3. Smart Contract Deployment

#### Install Dependencies
```bash
cd contracts
npm install
```

#### Configure Environment
```bash
# Create .env file
cp .env.example .env

# Edit environment variables
nano .env
```

```env
PRIVATE_KEY=your_deployer_private_key
INFURA_API_KEY=your_infura_api_key
ETHERSCAN_API_KEY=your_etherscan_api_key
```

#### Deploy Contracts
```bash
# Compile contracts
npx hardhat compile

# Deploy to testnet (Goerli)
npx hardhat run scripts/deploy.js --network goerli

# Deploy to mainnet
npx hardhat run scripts/deploy.js --network mainnet

# Verify contracts
npx hardhat verify --network mainnet CONTRACT_ADDRESS "CONSTRUCTOR_ARGS"
```

### 4. Backend Deployment

#### Create Virtual Environment
```bash
cd ../backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### Configure Environment
```bash
# Create .env file
cp .env.example .env

# Edit configuration
nano .env
```

```env
# Application
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your_super_secret_key_here

# Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=usdc_defi_user
DB_PASSWORD=secure_password_here
DB_NAME=usdc_defi

# Blockchain
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_project_id
USDC_CONTRACT_ADDRESS=******************************************
PLATFORM_CONTRACT_ADDRESS=deployed_contract_address
GAS_SPONSOR_WALLET=******************************************
GAS_SPONSOR_PRIVATE_KEY=gas_sponsor_private_key

# OpenZeppelin Defender
DEFENDER_API_KEY=your_defender_api_key
DEFENDER_API_SECRET=your_defender_api_secret
DEFENDER_RELAYER_ID=your_relayer_id

# CORS
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
```

#### Test Backend
```bash
# Run tests
python -m pytest

# Start development server
python main.py
```

### 5. Frontend Deployment

#### Install Dependencies
```bash
cd ../frontend
npm install
```

#### Configure Environment
```bash
# Create .env file
cp .env.example .env

# Edit configuration
nano .env
```

```env
REACT_APP_API_BASE_URL=https://your-domain.com/api/v1
REACT_APP_RAINBOWKIT_PROJECT_ID=1a54ba92caa7810745990910f7daccc4
REACT_APP_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_project_id
REACT_APP_USDC_CONTRACT_ADDRESS=******************************************
REACT_APP_PLATFORM_CONTRACT_ADDRESS=deployed_contract_address
REACT_APP_GAS_SPONSOR_WALLET=******************************************
```

#### Build Production Version
```bash
# Build application
npm run build

# Test build
npm run test
```

## 🌐 Production Deployment

### 1. PM2 Configuration

#### Setup PM2 Ecosystem
```bash
cd ..
cp deployment/pm2/ecosystem.config.js .

# Edit configuration
nano ecosystem.config.js
```

#### Start Applications
```bash
# Start all applications
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup startup script
pm2 startup
```

### 2. Nginx Configuration

#### Create Nginx Configuration
```bash
sudo cp deployment/nginx/usdc-defi.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/usdc-defi.conf /etc/nginx/sites-enabled/

# Edit configuration
sudo nano /etc/nginx/sites-available/usdc-defi.conf
```

#### Update Configuration
- Replace `your-domain.com` with your actual domain
- Update file paths to match your deployment
- Configure SSL certificate paths

#### Test and Reload Nginx
```bash
# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

### 3. SSL Certificate Setup

#### Using Let's Encrypt
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

#### Using Custom Certificate
```bash
# Copy certificate files
sudo mkdir -p /etc/ssl/certs/your-domain.com
sudo cp your-cert.pem /etc/ssl/certs/your-domain.com/
sudo cp your-key.pem /etc/ssl/private/your-domain.com/

# Update Nginx configuration
sudo nano /etc/nginx/sites-available/usdc-defi.conf
```

### 4. Security Configuration

#### Firewall Setup
```bash
# Install UFW
sudo apt install -y ufw

# Configure firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Enable firewall
sudo ufw enable
```

#### Fail2Ban Setup
```bash
# Install Fail2Ban
sudo apt install -y fail2ban

# Configure Fail2Ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
sudo nano /etc/fail2ban/jail.local

# Start Fail2Ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## 📊 Monitoring and Logging

### 1. Log Management

#### Create Log Directories
```bash
sudo mkdir -p /var/log/usdc-defi
sudo chown -R www-data:www-data /var/log/usdc-defi
```

#### Configure Log Rotation
```bash
sudo nano /etc/logrotate.d/usdc-defi
```

```
/var/log/usdc-defi/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload all
    endscript
}
```

### 2. Monitoring Setup

#### System Monitoring
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Setup monitoring script
cp scripts/monitor.sh /usr/local/bin/
chmod +x /usr/local/bin/monitor.sh

# Add to crontab
echo "*/5 * * * * /usr/local/bin/monitor.sh" | sudo crontab -
```

#### Application Monitoring
```bash
# PM2 monitoring
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30

# Setup health checks
pm2 install pm2-auto-pull
```

## 🔄 Backup and Recovery

### 1. Database Backup

#### Automated Backup Script
```bash
#!/bin/bash
# /usr/local/bin/backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/usdc-defi"
DB_NAME="usdc_defi"
DB_USER="usdc_defi_user"
DB_PASS="secure_password_here"

mkdir -p $BACKUP_DIR

# Create database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

#### Schedule Backup
```bash
# Make script executable
chmod +x /usr/local/bin/backup-db.sh

# Add to crontab (daily at 2 AM)
echo "0 2 * * * /usr/local/bin/backup-db.sh" | sudo crontab -
```

### 2. Application Backup

#### Code Backup
```bash
#!/bin/bash
# /usr/local/bin/backup-app.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/usdc-defi"
APP_DIR="/var/www/usdc-defi"

mkdir -p $BACKUP_DIR

# Create application backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz -C $APP_DIR .

# Remove backups older than 7 days
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +7 -delete

echo "Application backup completed: app_backup_$DATE.tar.gz"
```

## 🚀 Go Live Checklist

### Pre-Launch Checklist
- [ ] All contracts deployed and verified
- [ ] Database schema and data imported
- [ ] Backend API running and tested
- [ ] Frontend built and deployed
- [ ] SSL certificate installed and working
- [ ] DNS records configured correctly
- [ ] Monitoring and logging configured
- [ ] Backup systems tested
- [ ] Security measures implemented
- [ ] Performance testing completed

### Launch Day
- [ ] Final system health check
- [ ] Monitor error logs
- [ ] Check all API endpoints
- [ ] Verify wallet connections work
- [ ] Test deposit and withdrawal flows
- [ ] Monitor system performance
- [ ] Be ready for user support

### Post-Launch
- [ ] Monitor user activity
- [ ] Track system performance
- [ ] Review error logs daily
- [ ] Gather user feedback
- [ ] Plan feature updates
- [ ] Regular security audits

## 🆘 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check PM2 status
pm2 status

# Check logs
pm2 logs

# Restart application
pm2 restart all
```

#### Database Connection Issues
```bash
# Check MySQL status
sudo systemctl status mysql

# Test connection
mysql -u usdc_defi_user -p usdc_defi -e "SELECT 1"

# Check configuration
cat backend/.env | grep DB_
```

#### Nginx Issues
```bash
# Check Nginx status
sudo systemctl status nginx

# Test configuration
sudo nginx -t

# Check error logs
sudo tail -f /var/log/nginx/error.log
```

### Emergency Procedures

#### Platform Emergency Stop
```bash
# Stop all services
pm2 stop all
sudo systemctl stop nginx

# Enable maintenance page
sudo cp maintenance.html /var/www/html/index.html
sudo systemctl start nginx
```

#### Database Recovery
```bash
# Restore from backup
gunzip /var/backups/usdc-defi/db_backup_YYYYMMDD_HHMMSS.sql.gz
mysql -u usdc_defi_user -p usdc_defi < /var/backups/usdc-defi/db_backup_YYYYMMDD_HHMMSS.sql
```

## 📞 Support and Resources

### Documentation
- **API Documentation**: `/api/docs`
- **User Manual**: `docs/user-guide/user-manual.md`
- **Admin Guide**: `docs/user-guide/admin-guide.md`

### Support Contacts
- **Technical Support**: <EMAIL>
- **Emergency Contact**: +1-XXX-XXX-XXXX
- **Security Issues**: <EMAIL>

### External Resources
- **Ethereum Documentation**: https://ethereum.org/developers/
- **OpenZeppelin Docs**: https://docs.openzeppelin.com/
- **FastAPI Documentation**: https://fastapi.tiangolo.com/
- **React Documentation**: https://reactjs.org/docs/

---

*This deployment guide is regularly updated. For the latest version, visit the project repository.*
