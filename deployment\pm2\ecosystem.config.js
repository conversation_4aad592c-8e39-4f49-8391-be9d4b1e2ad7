// PM2 Ecosystem Configuration for USDC-DeFi Platform
module.exports = {
  apps: [
    {
      // Main FastAPI Backend Application
      name: 'usdc-defi-api',
      script: '/www/wwwroot/usdc-defi/backend/main.py',
      interpreter: '/www/server/pyproject_env/usdc-defi/bin/python',
      cwd: '/www/wwwroot/usdc-defi/backend',
      instances: 4, // Number of instances (adjust based on CPU cores)
      exec_mode: 'cluster',
      
      // Environment variables
      env: {
        NODE_ENV: 'production',
        ENVIRONMENT: 'production',
        HOST: '127.0.0.1',
        PORT: 8000,
        WORKERS: 4,
        
        // Database configuration
        DB_HOST: 'localhost',
        DB_PORT: 3306,
        DB_USER: 'usdc_defi_user',
        DB_PASSWORD: 'your_secure_password',
        DB_NAME: 'usdc_defi',
        
        // Security
        SECRET_KEY: 'your_super_secret_key_here_change_in_production',
        
        // Blockchain configuration
        ETHEREUM_RPC_URL: 'https://mainnet.infura.io/v3/your_infura_project_id',
        USDC_CONTRACT_ADDRESS: '******************************************',
        PLATFORM_CONTRACT_ADDRESS: '******************************************',
        GAS_SPONSOR_WALLET: '******************************************',
        GAS_SPONSOR_PRIVATE_KEY: 'your_gas_sponsor_private_key',
        
        // OpenZeppelin Defender
        DEFENDER_API_KEY: 'your_defender_api_key',
        DEFENDER_API_SECRET: 'your_defender_api_secret',
        DEFENDER_RELAYER_ID: 'your_defender_relayer_id',
        
        // RainbowKit
        RAINBOWKIT_PROJECT_ID: '1a54ba92caa7810745990910f7daccc4',
        
        // CORS and security
        ALLOWED_ORIGINS: 'https://your-domain.com,https://www.your-domain.com',
        ALLOWED_HOSTS: 'your-domain.com,www.your-domain.com',
        
        // Redis (if using)
        REDIS_URL: 'redis://localhost:6379/0',
        
        // Logging
        LOG_LEVEL: 'INFO',
        LOG_FILE: '/www/wwwlogs/usdc-defi/app.log',
        
        // Monitoring
        SENTRY_DSN: 'your_sentry_dsn_if_using',
      },
      
      // Development environment (for staging)
      env_development: {
        NODE_ENV: 'development',
        ENVIRONMENT: 'development',
        DEBUG: 'true',
        LOG_LEVEL: 'DEBUG',
        ETHEREUM_RPC_URL: 'https://goerli.infura.io/v3/your_infura_project_id',
        ALLOWED_ORIGINS: 'http://localhost:3000,https://staging.your-domain.com',
      },
      
      // PM2 configuration
      watch: false, // Set to true for development
      ignore_watch: [
        'node_modules',
        'logs',
        '*.log',
        '.git',
        '__pycache__',
        '*.pyc',
      ],
      
      // Restart configuration
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1G',
      
      // Logging
      log_file: '/www/wwwlogs/usdc-defi/combined.log',
      out_file: '/www/wwwlogs/usdc-defi/out.log',
      error_file: '/www/wwwlogs/usdc-defi/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Advanced options
      kill_timeout: 5000,
      listen_timeout: 8000,
      
      // Health monitoring
      health_check_grace_period: 3000,
      
      // Cron restart (optional - restart daily at 3 AM)
      cron_restart: '0 3 * * *',
      
      // Auto restart on file changes (development only)
      autorestart: true,
      
      // Merge logs
      merge_logs: true,
      
      // Time zone
      time: true,
    },
    
    {
      // Background Task Worker (for earnings distribution, etc.)
      name: 'usdc-defi-worker',
      script: '/www/wwwroot/usdc-defi/backend/worker.py',
      interpreter: '/www/server/pyproject_env/usdc-defi/bin/python',
      cwd: '/www/wwwroot/usdc-defi/backend',
      instances: 1,
      exec_mode: 'fork',
      
      // Environment variables (inherit from main app)
      env: {
        NODE_ENV: 'production',
        ENVIRONMENT: 'production',
        WORKER_TYPE: 'background',
        
        // Database configuration
        DB_HOST: 'localhost',
        DB_PORT: 3306,
        DB_USER: 'usdc_defi_user',
        DB_PASSWORD: 'your_secure_password',
        DB_NAME: 'usdc_defi',
        
        // Blockchain configuration
        ETHEREUM_RPC_URL: 'https://mainnet.infura.io/v3/your_infura_project_id',
        GAS_SPONSOR_PRIVATE_KEY: 'your_gas_sponsor_private_key',
        
        // Logging
        LOG_LEVEL: 'INFO',
        LOG_FILE: '/www/wwwlogs/usdc-defi/worker.log',
      },
      
      // PM2 configuration
      watch: false,
      max_restarts: 5,
      min_uptime: '30s',
      max_memory_restart: '512M',
      
      // Logging
      log_file: '/www/wwwlogs/usdc-defi/worker-combined.log',
      out_file: '/www/wwwlogs/usdc-defi/worker-out.log',
      error_file: '/www/wwwlogs/usdc-defi/worker-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Cron restart (restart twice daily)
      cron_restart: '0 */12 * * *',
      
      autorestart: true,
      merge_logs: true,
      time: true,
    },
    
    {
      // Demo Data Refresh Service
      name: 'usdc-defi-demo-refresh',
      script: '/www/wwwroot/usdc-defi/backend/scripts/refresh_demo_data.py',
      interpreter: '/www/server/pyproject_env/usdc-defi/bin/python',
      cwd: '/www/wwwroot/usdc-defi/backend',
      instances: 1,
      exec_mode: 'fork',
      
      // Environment variables
      env: {
        NODE_ENV: 'production',
        ENVIRONMENT: 'production',
        
        // Database configuration
        DB_HOST: 'localhost',
        DB_PORT: 3306,
        DB_USER: 'usdc_defi_user',
        DB_PASSWORD: 'your_secure_password',
        DB_NAME: 'usdc_defi',
        
        // Refresh interval (in seconds)
        REFRESH_INTERVAL: 300, // 5 minutes
        
        // Logging
        LOG_LEVEL: 'INFO',
        LOG_FILE: '/www/wwwlogs/usdc-defi/demo-refresh.log',
      },
      
      // PM2 configuration
      watch: false,
      max_restarts: 3,
      min_uptime: '60s',
      max_memory_restart: '256M',
      
      // Logging
      log_file: '/www/wwwlogs/usdc-defi/demo-refresh-combined.log',
      out_file: '/www/wwwlogs/usdc-defi/demo-refresh-out.log',
      error_file: '/www/wwwlogs/usdc-defi/demo-refresh-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      autorestart: true,
      merge_logs: true,
      time: true,
    }
  ],
  
  // Deployment configuration
  deploy: {
    production: {
      user: 'root',
      host: 'your-server-ip',
      ref: 'origin/main',
      repo: 'https://github.com/your-username/usdc-defi.git',
      path: '/www/wwwroot/usdc-defi',
      'pre-deploy-local': '',
      'post-deploy': 'cd backend && pip install -r requirements.txt && cd ../frontend && npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'root',
      host: 'your-staging-server-ip',
      ref: 'origin/develop',
      repo: 'https://github.com/your-username/usdc-defi.git',
      path: '/www/wwwroot/usdc-defi-staging',
      'post-deploy': 'cd backend && pip install -r requirements.txt && cd ../frontend && npm install && npm run build && pm2 reload ecosystem.config.js --env development',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
