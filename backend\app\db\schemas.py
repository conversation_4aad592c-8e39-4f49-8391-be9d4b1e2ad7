"""
Pydantic schemas for request/response models
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime, date
from decimal import Decimal
from enum import Enum

# Enums for status fields
class DepositStatusEnum(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    FAILED = "failed"

class EarningStatusEnum(str, Enum):
    PENDING = "pending"
    DISTRIBUTED = "distributed"
    FAILED = "failed"

class WithdrawalStatusEnum(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class WithdrawalTypeEnum(str, Enum):
    PRINCIPAL = "principal"
    EARNINGS = "earnings"
    BOTH = "both"

class TransactionTypeEnum(str, Enum):
    DEPOSIT = "deposit"
    WITHDRAWAL = "withdrawal"
    EARNING_DISTRIBUTION = "earning_distribution"

class TransactionStatusEnum(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    FAILED = "failed"

# Base schemas
class BaseSchema(BaseModel):
    class Config:
        from_attributes = True
        json_encoders = {
            Decimal: lambda v: float(v) if v is not None else None,
            datetime: lambda v: v.isoformat() if v is not None else None,
            date: lambda v: v.isoformat() if v is not None else None,
        }

# User schemas
class UserBase(BaseSchema):
    wallet_address: str = Field(..., min_length=42, max_length=42, description="Ethereum wallet address")

class UserCreate(UserBase):
    pass

class UserUpdate(BaseSchema):
    last_login_at: Optional[datetime] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime]
    is_active: bool
    total_deposited: Decimal
    total_earned: Decimal

class UserSummary(BaseSchema):
    id: int
    wallet_address: str
    total_deposited: Decimal
    total_earned: Decimal
    active_deposits: int
    last_login_at: Optional[datetime]

# Interest Tier schemas
class InterestTierBase(BaseSchema):
    tier_name: str
    min_amount: Decimal
    max_amount: Optional[Decimal]
    min_rate: Decimal = Field(..., ge=0, le=1, description="Minimum interest rate (0-1)")
    max_rate: Decimal = Field(..., ge=0, le=1, description="Maximum interest rate (0-1)")

class InterestTierCreate(InterestTierBase):
    pass

class InterestTierUpdate(BaseSchema):
    tier_name: Optional[str] = None
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None
    min_rate: Optional[Decimal] = None
    max_rate: Optional[Decimal] = None
    is_active: Optional[bool] = None

class InterestTierResponse(InterestTierBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

# Deposit schemas
class DepositBase(BaseSchema):
    amount: Decimal = Field(..., gt=0, description="Deposit amount in USDC")

class DepositCreate(DepositBase):
    transaction_hash: Optional[str] = Field(None, min_length=66, max_length=66)

class DepositUpdate(BaseSchema):
    status: Optional[DepositStatusEnum] = None
    transaction_hash: Optional[str] = None

class DepositResponse(DepositBase):
    id: int
    user_id: int
    interest_tier_id: int
    interest_rate: Decimal
    transaction_hash: Optional[str]
    status: DepositStatusEnum
    created_at: datetime
    updated_at: datetime

class DepositWithDetails(DepositResponse):
    user: UserResponse
    interest_tier: InterestTierResponse

# Earning schemas
class EarningBase(BaseSchema):
    amount: Decimal = Field(..., gt=0, description="Earning amount in USDC")
    earning_date: date
    interest_rate: Decimal

class EarningCreate(EarningBase):
    user_id: int
    deposit_id: int

class EarningUpdate(BaseSchema):
    status: Optional[EarningStatusEnum] = None
    transaction_hash: Optional[str] = None

class EarningResponse(EarningBase):
    id: int
    user_id: int
    deposit_id: int
    status: EarningStatusEnum
    transaction_hash: Optional[str]
    created_at: datetime
    updated_at: datetime

# Withdrawal schemas
class WithdrawalBase(BaseSchema):
    withdrawal_type: WithdrawalTypeEnum
    amount: Decimal = Field(..., gt=0, description="Withdrawal amount in USDC")

class WithdrawalCreate(WithdrawalBase):
    pass

class WithdrawalUpdate(BaseSchema):
    status: Optional[WithdrawalStatusEnum] = None
    transaction_hash: Optional[str] = None
    processed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

class WithdrawalResponse(WithdrawalBase):
    id: int
    user_id: int
    status: WithdrawalStatusEnum
    transaction_hash: Optional[str]
    requested_at: datetime
    processed_at: Optional[datetime]
    completed_at: Optional[datetime]

# Transaction schemas
class TransactionBase(BaseSchema):
    transaction_hash: str = Field(..., min_length=66, max_length=66)
    transaction_type: TransactionTypeEnum
    amount: Decimal
    gas_fee: Decimal = Field(default=Decimal('0.000000'))

class TransactionCreate(TransactionBase):
    user_id: int

class TransactionUpdate(BaseSchema):
    status: Optional[TransactionStatusEnum] = None
    block_number: Optional[int] = None
    block_timestamp: Optional[datetime] = None

class TransactionResponse(TransactionBase):
    id: int
    user_id: int
    block_number: Optional[int]
    block_timestamp: Optional[datetime]
    status: TransactionStatusEnum
    created_at: datetime
    updated_at: datetime

# Demo Deposit schemas
class DemoDepositBase(BaseSchema):
    wallet_address: str
    amount: Decimal
    display_time: str

class DemoDepositCreate(DemoDepositBase):
    pass

class DemoDepositResponse(DemoDepositBase):
    id: int
    created_at: datetime
    is_active: bool

# System Config schemas
class SystemConfigBase(BaseSchema):
    config_key: str
    config_value: str
    description: Optional[str] = None

class SystemConfigCreate(SystemConfigBase):
    pass

class SystemConfigUpdate(BaseSchema):
    config_value: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class SystemConfigResponse(SystemConfigBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

# Authentication schemas
class WalletSignatureRequest(BaseSchema):
    wallet_address: str = Field(..., min_length=42, max_length=42)
    signature: str = Field(..., description="Wallet signature for authentication")
    message: str = Field(..., description="Original message that was signed")
    timestamp: int = Field(..., description="Unix timestamp when signature was created")

class AuthResponse(BaseSchema):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse

# Dashboard schemas
class DashboardStats(BaseSchema):
    total_users: int
    total_deposits: Decimal
    total_earnings_distributed: Decimal
    active_deposits: int
    platform_tvl: Decimal

class UserDashboard(BaseSchema):
    user: UserResponse
    active_deposits: List[DepositResponse]
    recent_earnings: List[EarningResponse]
    total_pending_earnings: Decimal
    current_apy: Optional[Decimal]

# API Response schemas
class APIResponse(BaseSchema):
    success: bool
    message: str
    data: Optional[dict] = None

class PaginatedResponse(BaseSchema):
    items: List[dict]
    total: int
    page: int
    size: int
    pages: int

# Validators
@validator('wallet_address')
def validate_wallet_address(cls, v):
    if not v.startswith('0x'):
        raise ValueError('Wallet address must start with 0x')
    if len(v) != 42:
        raise ValueError('Wallet address must be 42 characters long')
    return v.lower()

# Apply validator to all wallet address fields
UserBase.wallet_address = Field(..., validator=validate_wallet_address)
WalletSignatureRequest.wallet_address = Field(..., validator=validate_wallet_address)
