import React from 'react';
import {
  Box,
  Container,
  Typography,
  Link,
  Grid,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Twitter,
  Telegram,
  GitHub,
  Description,
  Security,
  Help,
} from '@mui/icons-material';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    platform: [
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Deposit', href: '/deposit' },
      { label: 'Earnings', href: '/earnings' },
      { label: 'Withdraw', href: '/withdraw' },
    ],
    resources: [
      { label: 'Whitepaper', href: '/whitepaper', icon: <Description /> },
      { label: 'Audit Report', href: '/audit', icon: <Security /> },
      { label: 'Help Center', href: '/help', icon: <Help /> },
      { label: 'Support', href: '/support' },
    ],
    legal: [
      { label: 'Terms of Service', href: '/terms' },
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Risk Disclosure', href: '/risks' },
      { label: 'Compliance', href: '/compliance' },
    ],
  };

  const socialLinks = [
    {
      name: 'Twitter',
      icon: <Twitter />,
      url: process.env.REACT_APP_TWITTER_URL || 'https://twitter.com/usdcdefi',
    },
    {
      name: 'Telegram',
      icon: <Telegram />,
      url: process.env.REACT_APP_TELEGRAM_URL || 'https://t.me/usdcdefi',
    },
    {
      name: 'GitHub',
      icon: <GitHub />,
      url: process.env.REACT_APP_GITHUB_URL || 'https://github.com/usdcdefi',
    },
  ];

  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: 'background.paper',
        borderTop: 1,
        borderColor: 'divider',
        mt: 'auto',
        py: 6,
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Brand Section */}
          <Grid item xs={12} md={4}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                background: 'linear-gradient(135deg, #0066ff 0%, #0052cc 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
              }}
            >
              USDC-DeFi Mining Platform
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Earn competitive yields on your USDC deposits with our secure, 
              gas-free DeFi mining platform. Up to 4.1% APY with tiered interest rates.
            </Typography>
            
            {/* Social Links */}
            <Box sx={{ display: 'flex', gap: 1 }}>
              {socialLinks.map((social) => (
                <IconButton
                  key={social.name}
                  component="a"
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  size="small"
                  sx={{
                    color: 'text.secondary',
                    '&:hover': {
                      color: 'primary.main',
                      backgroundColor: 'action.hover',
                    },
                  }}
                >
                  {social.icon}
                </IconButton>
              ))}
            </Box>
          </Grid>

          {/* Platform Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Platform
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {footerLinks.platform.map((link) => (
                <Link
                  key={link.label}
                  href={link.href}
                  color="text.secondary"
                  underline="none"
                  sx={{
                    '&:hover': {
                      color: 'primary.main',
                      textDecoration: 'underline',
                    },
                  }}
                >
                  {link.label}
                </Link>
              ))}
            </Box>
          </Grid>

          {/* Resources Links */}
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Resources
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {footerLinks.resources.map((link) => (
                <Link
                  key={link.label}
                  href={link.href}
                  color="text.secondary"
                  underline="none"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    '&:hover': {
                      color: 'primary.main',
                      textDecoration: 'underline',
                    },
                  }}
                >
                  {link.icon && (
                    <Box sx={{ fontSize: '1rem' }}>
                      {link.icon}
                    </Box>
                  )}
                  {link.label}
                </Link>
              ))}
            </Box>
          </Grid>

          {/* Legal Links */}
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Legal
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {footerLinks.legal.map((link) => (
                <Link
                  key={link.label}
                  href={link.href}
                  color="text.secondary"
                  underline="none"
                  sx={{
                    '&:hover': {
                      color: 'primary.main',
                      textDecoration: 'underline',
                    },
                  }}
                >
                  {link.label}
                </Link>
              ))}
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4 }} />

        {/* Bottom Section */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2,
          }}
        >
          <Typography variant="body2" color="text.secondary">
            © {currentYear} USDC-DeFi Mining Platform. All rights reserved.
          </Typography>

          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 1, sm: 3 },
              alignItems: { xs: 'flex-start', sm: 'center' },
            }}
          >
            <Typography variant="body2" color="text.secondary">
              Version 1.0.0
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Built with ❤️ for DeFi
            </Typography>
            <Link
              href={`mailto:${process.env.REACT_APP_SUPPORT_EMAIL || '<EMAIL>'}`}
              color="text.secondary"
              underline="none"
              sx={{
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline',
                },
              }}
            >
              Contact Support
            </Link>
          </Box>
        </Box>

        {/* Disclaimer */}
        <Box sx={{ mt: 3, p: 2, backgroundColor: 'action.hover', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
            <strong>Risk Disclaimer:</strong> DeFi investments carry inherent risks including smart contract vulnerabilities, 
            market volatility, and potential loss of funds. Past performance does not guarantee future results. 
            Please read our risk disclosure and only invest what you can afford to lose.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
