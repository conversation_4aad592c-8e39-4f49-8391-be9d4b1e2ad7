import React from 'react';
import { Container, Typography, Box, Grid, Card, CardContent } from '@mui/material';
import { AccountBalance, TrendingUp, Paid } from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  const stats = [
    {
      title: 'Total Deposited',
      value: user?.total_deposited || '0',
      icon: <AccountBalance />,
      color: '#0066ff',
    },
    {
      title: 'Total Earned',
      value: user?.total_earned || '0',
      icon: <TrendingUp />,
      color: '#10b981',
    },
    {
      title: 'Current APY',
      value: '2.5%',
      icon: <Paid />,
      color: '#f59e0b',
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Welcome back! Here's your DeFi mining overview.
      </Typography>

      <Grid container spacing={3}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ color: stat.color, mr: 1 }}>
                    {stat.icon}
                  </Box>
                  <Typography variant="h6">{stat.title}</Typography>
                </Box>
                <Typography variant="h4" sx={{ color: stat.color }}>
                  {stat.value}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" gutterBottom>
          Recent Activity
        </Typography>
        <Card>
          <CardContent>
            <Typography variant="body1" color="text.secondary">
              No recent activity. Start by making your first deposit!
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default DashboardPage;
