import React from 'react';
import { Box, Container, Typography, Grid, Card, CardContent } from '@mui/material';
import { Security, Speed, TrendingUp, AccountBalance } from '@mui/icons-material';

const FeaturesSection: React.FC = () => {
  const features = [
    {
      icon: <Security />,
      title: 'Secure & Audited',
      description: 'Smart contracts professionally audited for maximum security',
    },
    {
      icon: <Speed />,
      title: 'Gas-Free Transactions',
      description: 'All transaction fees sponsored by the platform',
    },
    {
      icon: <TrendingUp />,
      title: 'Competitive Yields',
      description: 'Up to 4.1% APY with tiered interest rates',
    },
    {
      icon: <AccountBalance />,
      title: 'Instant Liquidity',
      description: 'Withdraw your funds anytime without penalties',
    },
  ];

  return (
    <Box sx={{ py: 8, backgroundColor: 'background.paper' }}>
      <Container maxWidth="lg">
        <Typography variant="h3" textAlign="center" gutterBottom sx={{ mb: 6 }}>
          Why Choose USDC-DeFi?
        </Typography>
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card sx={{ height: '100%', textAlign: 'center' }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ color: 'primary.main', mb: 2 }}>
                    {feature.icon}
                  </Box>
                  <Typography variant="h6" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default FeaturesSection;
