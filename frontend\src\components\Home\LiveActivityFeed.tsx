import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Avatar,
  Stack,
  useTheme,
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { AccountBalanceWallet, TrendingUp } from '@mui/icons-material';

// Hooks
import { useDemoData } from '../../hooks/useDemoData';

// Types
interface ActivityItem {
  id: number;
  wallet_address: string;
  amount: string;
  amount_raw: number;
  display_time: string;
  activity_type: string;
  formatted_text: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
  exit: {
    opacity: 0,
    x: 20,
    transition: {
      duration: 0.3,
    },
  },
};

const LiveActivityFeed: React.FC = () => {
  const theme = useTheme();
  const { demoData, isLoading, error, refreshData } = useDemoData();
  const [displayedItems, setDisplayedItems] = useState<ActivityItem[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  // Number of items to display at once
  const ITEMS_TO_SHOW = 6;
  const SCROLL_INTERVAL = 3000; // 3 seconds

  // Initialize displayed items
  useEffect(() => {
    if (demoData && demoData.length > 0) {
      setDisplayedItems(demoData.slice(0, ITEMS_TO_SHOW));
    }
  }, [demoData]);

  // Auto-scroll functionality
  useEffect(() => {
    if (!demoData || demoData.length <= ITEMS_TO_SHOW || isPaused) {
      return;
    }

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % demoData.length;
        const newItems = [];
        
        for (let i = 0; i < ITEMS_TO_SHOW; i++) {
          const itemIndex = (nextIndex + i) % demoData.length;
          newItems.push(demoData[itemIndex]);
        }
        
        setDisplayedItems(newItems);
        return nextIndex;
      });
    }, SCROLL_INTERVAL);

    return () => clearInterval(interval);
  }, [demoData, isPaused, ITEMS_TO_SHOW]);

  // Refresh data periodically
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      refreshData();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(refreshInterval);
  }, [refreshData]);

  const formatAmount = (amount: string): string => {
    const num = parseFloat(amount.replace(/,/g, ''));
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return amount;
  };

  const getAmountColor = (amount: number): string => {
    if (amount >= 1000000) return theme.palette.error.main;
    if (amount >= 100000) return theme.palette.warning.main;
    if (amount >= 10000) return theme.palette.info.main;
    return theme.palette.success.main;
  };

  const getTierLabel = (amount: number): string => {
    if (amount >= 2000000) return 'Tier 9';
    if (amount >= 1000000) return 'Tier 8';
    if (amount >= 500000) return 'Tier 7';
    if (amount >= 200000) return 'Tier 6';
    if (amount >= 100000) return 'Tier 5';
    if (amount >= 50000) return 'Tier 4';
    if (amount >= 20000) return 'Tier 3';
    if (amount >= 5000) return 'Tier 2';
    return 'Tier 1';
  };

  if (isLoading) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="body1" color="text.secondary">
          Loading live activity...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="body1" color="error">
          Unable to load activity feed
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
      sx={{
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 2,
        backgroundColor: 'background.paper',
        border: 1,
        borderColor: 'divider',
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 3,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Avatar sx={{ backgroundColor: 'primary.main' }}>
          <TrendingUp />
        </Avatar>
        <Box>
          <Typography variant="h6" component="h3">
            Live Deposit Activity
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Real-time platform deposits • Updates every few seconds
          </Typography>
        </Box>
        {isPaused && (
          <Chip
            label="Paused"
            size="small"
            color="warning"
            sx={{ ml: 'auto' }}
          />
        )}
      </Box>

      {/* Activity Items */}
      <Box sx={{ p: 2 }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <AnimatePresence mode="wait">
            <Stack spacing={2}>
              {displayedItems.map((item, index) => (
                <motion.div
                  key={`${item.id}-${currentIndex}-${index}`}
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  layout
                >
                  <Card
                    variant="outlined"
                    sx={{
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        boxShadow: 2,
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    <CardContent sx={{ py: 2, '&:last-child': { pb: 2 } }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          gap: 2,
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            sx={{
                              width: 32,
                              height: 32,
                              backgroundColor: 'primary.main',
                              fontSize: '0.875rem',
                            }}
                          >
                            <AccountBalanceWallet fontSize="small" />
                          </Avatar>
                          <Box>
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'monospace',
                                color: 'text.primary',
                                fontWeight: 500,
                              }}
                            >
                              {item.wallet_address}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {item.display_time}
                            </Typography>
                          </Box>
                        </Box>

                        <Box sx={{ textAlign: 'right' }}>
                          <Typography
                            variant="body1"
                            sx={{
                              fontWeight: 600,
                              color: getAmountColor(item.amount_raw),
                            }}
                          >
                            {formatAmount(item.amount)} USDC
                          </Typography>
                          <Chip
                            label={getTierLabel(item.amount_raw)}
                            size="small"
                            variant="outlined"
                            sx={{
                              fontSize: '0.75rem',
                              height: 20,
                              color: getAmountColor(item.amount_raw),
                              borderColor: getAmountColor(item.amount_raw),
                            }}
                          />
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </Stack>
          </AnimatePresence>
        </motion.div>
      </Box>

      {/* Footer */}
      <Box
        sx={{
          p: 2,
          borderTop: 1,
          borderColor: 'divider',
          backgroundColor: 'action.hover',
          textAlign: 'center',
        }}
      >
        <Typography variant="caption" color="text.secondary">
          Hover to pause • Showing {displayedItems.length} of {demoData?.length || 0} recent deposits
        </Typography>
      </Box>

      {/* Scroll indicator */}
      {!isPaused && demoData && demoData.length > ITEMS_TO_SHOW && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: 4,
            height: '100%',
            backgroundColor: 'primary.main',
            opacity: 0.3,
          }}
        >
          <motion.div
            style={{
              width: '100%',
              height: '20%',
              backgroundColor: theme.palette.primary.main,
            }}
            animate={{
              y: ['0%', '400%'],
            }}
            transition={{
              duration: SCROLL_INTERVAL / 1000,
              repeat: Infinity,
              ease: 'linear',
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default LiveActivityFeed;
