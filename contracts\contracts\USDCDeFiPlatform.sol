// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/metatx/ERC2771Context.sol";
import "@openzeppelin/contracts/metatx/MinimalForwarder.sol";

/**
 * @title USDCDeFiPlatform
 * @dev A DeFi mining platform for USDC deposits with tiered interest rates and meta-transaction support
 */
contract USDCDeFiPlatform is ERC2771Context, ReentrancyGuard, Pausable, Ownable {
    using SafeERC20 for IERC20;

    // USDC token contract
    IERC20 public immutable usdcToken;
    
    // Gas sponsor wallet for meta-transactions
    address public gasSponsor;
    
    // Platform fee recipient
    address public feeRecipient;
    
    // Platform fee rate (basis points, e.g., 100 = 1%)
    uint256 public platformFeeRate;
    
    // Interest rate tiers
    struct InterestTier {
        uint256 minAmount;
        uint256 maxAmount;
        uint256 minRate; // Annual rate in basis points (e.g., 130 = 1.3%)
        uint256 maxRate; // Annual rate in basis points (e.g., 160 = 1.6%)
        bool active;
    }
    
    // User deposit information
    struct Deposit {
        uint256 amount;
        uint256 interestRate; // Applied rate in basis points
        uint256 depositTime;
        uint256 lastClaimTime;
        bool active;
    }
    
    // User information
    struct UserInfo {
        uint256 totalDeposited;
        uint256 totalEarned;
        uint256 activeDeposits;
        mapping(uint256 => Deposit) deposits;
    }
    
    // State variables
    mapping(address => UserInfo) public users;
    mapping(uint256 => InterestTier) public interestTiers;
    uint256 public tierCount;
    uint256 public totalValueLocked;
    uint256 public totalEarningsDistributed;
    uint256 public nextDepositId;
    
    // Events
    event DepositMade(address indexed user, uint256 indexed depositId, uint256 amount, uint256 interestRate);
    event EarningsClaimed(address indexed user, uint256 indexed depositId, uint256 amount);
    event WithdrawalMade(address indexed user, uint256 indexed depositId, uint256 amount);
    event InterestTierUpdated(uint256 indexed tierId, uint256 minAmount, uint256 maxAmount, uint256 minRate, uint256 maxRate);
    event GasSponsorUpdated(address indexed oldSponsor, address indexed newSponsor);
    event PlatformFeeUpdated(uint256 oldRate, uint256 newRate);
    
    // Modifiers
    modifier onlyGasSponsor() {
        require(_msgSender() == gasSponsor, "Only gas sponsor can call this function");
        _;
    }
    
    modifier validDepositId(address user, uint256 depositId) {
        require(users[user].deposits[depositId].active, "Invalid or inactive deposit");
        _;
    }

    /**
     * @dev Constructor
     * @param _usdcToken USDC token contract address
     * @param _trustedForwarder Trusted forwarder for meta-transactions
     * @param _gasSponsor Gas sponsor wallet address
     */
    constructor(
        address _usdcToken,
        address _trustedForwarder,
        address _gasSponsor
    ) ERC2771Context(_trustedForwarder) {
        require(_usdcToken != address(0), "Invalid USDC token address");
        require(_gasSponsor != address(0), "Invalid gas sponsor address");
        
        usdcToken = IERC20(_usdcToken);
        gasSponsor = _gasSponsor;
        feeRecipient = owner();
        platformFeeRate = 0; // 0% initially
        
        // Initialize default interest tiers
        _initializeInterestTiers();
    }

    /**
     * @dev Initialize default interest rate tiers
     */
    function _initializeInterestTiers() private {
        // Tier 1: 100 - 4,999 USDC = 1.3% - 1.6%
        interestTiers[0] = InterestTier(100 * 1e6, 4999 * 1e6, 130, 160, true);
        
        // Tier 2: 5,000 - 19,999 USDC = 1.6% - 1.9%
        interestTiers[1] = InterestTier(5000 * 1e6, 19999 * 1e6, 160, 190, true);
        
        // Tier 3: 20,000 - 49,999 USDC = 1.9% - 2.2%
        interestTiers[2] = InterestTier(20000 * 1e6, 49999 * 1e6, 190, 220, true);
        
        // Tier 4: 50,000 - 99,999 USDC = 2.2% - 2.5%
        interestTiers[3] = InterestTier(50000 * 1e6, 99999 * 1e6, 220, 250, true);
        
        // Tier 5: 100,000 - 199,999 USDC = 2.5% - 2.8%
        interestTiers[4] = InterestTier(100000 * 1e6, 199999 * 1e6, 250, 280, true);
        
        // Tier 6: 200,000 - 499,999 USDC = 2.8% - 3.1%
        interestTiers[5] = InterestTier(200000 * 1e6, 499999 * 1e6, 280, 310, true);
        
        // Tier 7: 500,000 - 999,999 USDC = 3.1% - 3.5%
        interestTiers[6] = InterestTier(500000 * 1e6, 999999 * 1e6, 310, 350, true);
        
        // Tier 8: 1,000,000 - 1,999,999 USDC = 3.5% - 3.8%
        interestTiers[7] = InterestTier(1000000 * 1e6, 1999999 * 1e6, 350, 380, true);
        
        // Tier 9: 2,000,000+ USDC = 4.1%
        interestTiers[8] = InterestTier(2000000 * 1e6, type(uint256).max, 410, 410, true);
        
        tierCount = 9;
    }

    /**
     * @dev Make a deposit
     * @param amount Amount of USDC to deposit
     */
    function deposit(uint256 amount) external nonReentrant whenNotPaused {
        require(amount > 0, "Amount must be greater than 0");
        
        address user = _msgSender();
        uint256 interestRate = getInterestRate(amount);
        require(interestRate > 0, "Amount below minimum deposit threshold");
        
        // Transfer USDC from user to contract
        usdcToken.safeTransferFrom(user, address(this), amount);
        
        // Create deposit record
        uint256 depositId = nextDepositId++;
        users[user].deposits[depositId] = Deposit({
            amount: amount,
            interestRate: interestRate,
            depositTime: block.timestamp,
            lastClaimTime: block.timestamp,
            active: true
        });
        
        // Update user stats
        users[user].totalDeposited += amount;
        users[user].activeDeposits++;
        
        // Update global stats
        totalValueLocked += amount;
        
        emit DepositMade(user, depositId, amount, interestRate);
    }

    /**
     * @dev Claim earnings for a specific deposit
     * @param depositId Deposit ID to claim earnings for
     */
    function claimEarnings(uint256 depositId) external nonReentrant validDepositId(_msgSender(), depositId) {
        address user = _msgSender();
        Deposit storage userDeposit = users[user].deposits[depositId];
        
        uint256 earnings = calculateEarnings(user, depositId);
        require(earnings > 0, "No earnings to claim");
        
        // Update last claim time
        userDeposit.lastClaimTime = block.timestamp;
        
        // Calculate platform fee
        uint256 fee = (earnings * platformFeeRate) / 10000;
        uint256 netEarnings = earnings - fee;
        
        // Update user stats
        users[user].totalEarned += netEarnings;
        totalEarningsDistributed += netEarnings;
        
        // Transfer earnings to user
        usdcToken.safeTransfer(user, netEarnings);
        
        // Transfer fee to fee recipient
        if (fee > 0) {
            usdcToken.safeTransfer(feeRecipient, fee);
        }
        
        emit EarningsClaimed(user, depositId, netEarnings);
    }

    /**
     * @dev Withdraw principal amount
     * @param depositId Deposit ID to withdraw from
     */
    function withdraw(uint256 depositId) external nonReentrant validDepositId(_msgSender(), depositId) {
        address user = _msgSender();
        Deposit storage userDeposit = users[user].deposits[depositId];
        
        uint256 amount = userDeposit.amount;
        
        // Claim any pending earnings first
        uint256 earnings = calculateEarnings(user, depositId);
        if (earnings > 0) {
            uint256 fee = (earnings * platformFeeRate) / 10000;
            uint256 netEarnings = earnings - fee;
            
            users[user].totalEarned += netEarnings;
            totalEarningsDistributed += netEarnings;
            
            if (fee > 0) {
                usdcToken.safeTransfer(feeRecipient, fee);
            }
            
            amount += netEarnings;
        }
        
        // Mark deposit as inactive
        userDeposit.active = false;
        users[user].activeDeposits--;
        totalValueLocked -= userDeposit.amount;
        
        // Transfer total amount to user
        usdcToken.safeTransfer(user, amount);
        
        emit WithdrawalMade(user, depositId, amount);
    }

    /**
     * @dev Calculate earnings for a specific deposit
     * @param user User address
     * @param depositId Deposit ID
     * @return earnings Calculated earnings amount
     */
    function calculateEarnings(address user, uint256 depositId) public view returns (uint256 earnings) {
        Deposit memory userDeposit = users[user].deposits[depositId];
        if (!userDeposit.active) return 0;
        
        uint256 timeElapsed = block.timestamp - userDeposit.lastClaimTime;
        uint256 annualEarnings = (userDeposit.amount * userDeposit.interestRate) / 10000;
        earnings = (annualEarnings * timeElapsed) / 365 days;
    }

    /**
     * @dev Get interest rate for a given amount
     * @param amount Deposit amount
     * @return rate Interest rate in basis points
     */
    function getInterestRate(uint256 amount) public view returns (uint256 rate) {
        for (uint256 i = 0; i < tierCount; i++) {
            InterestTier memory tier = interestTiers[i];
            if (tier.active && amount >= tier.minAmount && amount <= tier.maxAmount) {
                // For simplicity, return the maximum rate of the tier
                // In practice, you might want to interpolate based on amount
                return tier.maxRate;
            }
        }
        return 0; // Below minimum threshold
    }

    /**
     * @dev Get user deposit information
     * @param user User address
     * @param depositId Deposit ID
     * @return deposit Deposit information
     */
    function getUserDeposit(address user, uint256 depositId) external view returns (Deposit memory deposit) {
        return users[user].deposits[depositId];
    }

    /**
     * @dev Get user summary
     * @param user User address
     * @return totalDeposited Total amount deposited
     * @return totalEarned Total earnings claimed
     * @return activeDeposits Number of active deposits
     */
    function getUserSummary(address user) external view returns (
        uint256 totalDeposited,
        uint256 totalEarned,
        uint256 activeDeposits
    ) {
        UserInfo storage userInfo = users[user];
        return (userInfo.totalDeposited, userInfo.totalEarned, userInfo.activeDeposits);
    }

    // Admin functions
    function updateInterestTier(
        uint256 tierId,
        uint256 minAmount,
        uint256 maxAmount,
        uint256 minRate,
        uint256 maxRate,
        bool active
    ) external onlyOwner {
        require(tierId < tierCount, "Invalid tier ID");
        
        interestTiers[tierId] = InterestTier(minAmount, maxAmount, minRate, maxRate, active);
        
        emit InterestTierUpdated(tierId, minAmount, maxAmount, minRate, maxRate);
    }

    function updateGasSponsor(address newGasSponsor) external onlyOwner {
        require(newGasSponsor != address(0), "Invalid gas sponsor address");
        
        address oldSponsor = gasSponsor;
        gasSponsor = newGasSponsor;
        
        emit GasSponsorUpdated(oldSponsor, newGasSponsor);
    }

    function updatePlatformFee(uint256 newFeeRate) external onlyOwner {
        require(newFeeRate <= 1000, "Fee rate cannot exceed 10%"); // Max 10%
        
        uint256 oldRate = platformFeeRate;
        platformFeeRate = newFeeRate;
        
        emit PlatformFeeUpdated(oldRate, newFeeRate);
    }

    function updateFeeRecipient(address newFeeRecipient) external onlyOwner {
        require(newFeeRecipient != address(0), "Invalid fee recipient address");
        feeRecipient = newFeeRecipient;
    }

    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // Emergency withdrawal function (only owner)
    function emergencyWithdraw(uint256 amount) external onlyOwner {
        usdcToken.safeTransfer(owner(), amount);
    }

    // Override required by Solidity
    function _msgSender() internal view override(Context, ERC2771Context) returns (address sender) {
        return ERC2771Context._msgSender();
    }

    function _msgData() internal view override(Context, ERC2771Context) returns (bytes calldata) {
        return ERC2771Context._msgData();
    }
}
