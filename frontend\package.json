{"name": "usdc-defi-frontend", "version": "1.0.0", "description": "USDC-DeFi Mining Platform Frontend", "private": true, "dependencies": {"@rainbow-me/rainbowkit": "^1.3.0", "@tanstack/react-query": "^4.36.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "wagmi": "^1.4.12", "viem": "^1.19.9", "ethers": "^6.8.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@mui/lab": "^5.0.0-alpha.155", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-countup": "^6.5.0", "react-intersection-observer": "^9.5.3", "react-helmet-async": "^2.0.4", "web-vitals": "^3.5.0", "typescript": "^4.9.5", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.0", "sass": "^1.69.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,scss,md}", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}