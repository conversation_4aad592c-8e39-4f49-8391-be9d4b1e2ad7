import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';

// Types
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

interface AuthTokens {
  access_token: string;
  token_type: string;
  expires_in: number;
}

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/v1';

// Create axios instance
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
class TokenManager {
  private static readonly TOKEN_KEY = 'usdc_defi_token';
  private static readonly REFRESH_KEY = 'usdc_defi_refresh';

  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_KEY);
  }

  static setRefreshToken(token: string): void {
    localStorage.setItem(this.REFRESH_KEY, token);
  }

  static clearTokens(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_KEY);
  }

  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }
}

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = TokenManager.getToken();
    if (token && !TokenManager.isTokenExpired(token)) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshToken = TokenManager.getRefreshToken();
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken,
          });

          const { access_token } = response.data;
          TokenManager.setToken(access_token);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, clear tokens and redirect to login
        TokenManager.clearTokens();
        window.location.href = '/';
        return Promise.reject(refreshError);
      }
    }

    // Handle other errors
    if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.status === 429) {
      toast.error('Too many requests. Please wait a moment.');
    } else if (error.code === 'NETWORK_ERROR') {
      toast.error('Network error. Please check your connection.');
    }

    return Promise.reject(error);
  }
);

// API methods
export const api = {
  // Authentication
  auth: {
    generateMessage: (walletAddress: string) =>
      apiClient.post('/auth/generate-message', { wallet_address: walletAddress }),

    walletLogin: (data: {
      wallet_address: string;
      signature: string;
      message: string;
      timestamp: number;
    }) => apiClient.post<ApiResponse<AuthTokens>>('/auth/wallet-login', data),

    verifySignature: (data: {
      wallet_address: string;
      signature: string;
      message: string;
      timestamp: number;
    }) => apiClient.post('/auth/verify-signature', data),

    getCurrentUser: () => apiClient.get('/auth/me'),

    refreshToken: () => apiClient.post('/auth/refresh'),

    logout: () => apiClient.post('/auth/logout'),
  },

  // Demo data
  demo: {
    getDeposits: (params?: { limit?: number; active_only?: boolean }) =>
      apiClient.get('/demo/deposits', { params }),

    getRandomDeposits: (count: number = 10) =>
      apiClient.get(`/demo/deposits/random?count=${count}`),

    getRecentDeposits: (params?: { minutes?: number; limit?: number }) =>
      apiClient.get('/demo/deposits/recent', { params }),

    getStats: () => apiClient.get('/demo/stats'),

    refreshData: () => apiClient.post('/demo/refresh'),

    getActivityFeed: (count: number = 15) =>
      apiClient.get(`/demo/activity-feed?count=${count}`),

    getDepositsByAmount: (params?: {
      min_amount?: number;
      max_amount?: number;
      limit?: number;
    }) => apiClient.get('/demo/deposits/by-amount', { params }),
  },

  // User management
  users: {
    getProfile: () => apiClient.get('/users/me'),

    updateProfile: (data: any) => apiClient.put('/users/me', data),

    getDashboard: () => apiClient.get('/users/dashboard'),

    getBalance: () => apiClient.get('/users/balance'),
  },

  // Deposits
  deposits: {
    create: (data: { amount: number; transaction_hash?: string }) =>
      apiClient.post('/deposits', data),

    getAll: (params?: { page?: number; size?: number }) =>
      apiClient.get('/deposits', { params }),

    getById: (id: number) => apiClient.get(`/deposits/${id}`),

    update: (id: number, data: any) => apiClient.put(`/deposits/${id}`, data),
  },

  // Earnings
  earnings: {
    getAll: (params?: { page?: number; size?: number; date_from?: string; date_to?: string }) =>
      apiClient.get('/earnings', { params }),

    getById: (id: number) => apiClient.get(`/earnings/${id}`),

    getTotalEarnings: () => apiClient.get('/earnings/total'),

    getEarningsHistory: (params?: { days?: number }) =>
      apiClient.get('/earnings/history', { params }),
  },

  // Withdrawals
  withdrawals: {
    create: (data: { withdrawal_type: string; amount: number }) =>
      apiClient.post('/withdrawals', data),

    getAll: (params?: { page?: number; size?: number }) =>
      apiClient.get('/withdrawals', { params }),

    getById: (id: number) => apiClient.get(`/withdrawals/${id}`),

    cancel: (id: number) => apiClient.post(`/withdrawals/${id}/cancel`),
  },

  // Dashboard
  dashboard: {
    getStats: () => apiClient.get('/dashboard/stats'),

    getUserDashboard: () => apiClient.get('/dashboard/user'),

    getPlatformStats: () => apiClient.get('/dashboard/platform'),
  },

  // Admin (if needed)
  admin: {
    getUsers: (params?: { page?: number; size?: number }) =>
      apiClient.get('/admin/users', { params }),

    getSystemStats: () => apiClient.get('/admin/stats'),

    updateConfig: (data: any) => apiClient.put('/admin/config', data),
  },
};

// Utility functions
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export const isApiError = (error: any): boolean => {
  return error.response && error.response.status >= 400;
};

// Export token manager for use in other parts of the app
export { TokenManager };

// Default export
export default apiClient;
