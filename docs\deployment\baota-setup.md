# USDC-DeFi Platform - 宝塔面板部署指南

本指南将详细介绍如何在宝塔面板上部署USDC-DeFi挖矿平台。

## 📋 系统要求

### 服务器配置
- **操作系统**: CentOS 7.x / Ubuntu 18.04+ / Debian 9+
- **内存**: 最低 4GB，推荐 8GB+
- **CPU**: 最低 2核，推荐 4核+
- **存储**: 最低 50GB SSD
- **带宽**: 最低 10Mbps

### 软件要求
- 宝塔面板 7.x+
- Python 3.8+
- Node.js 16+
- MySQL 8.0
- Nginx 1.18+
- PM2
- Redis (可选)

## 🚀 第一步：安装宝塔面板

### 1.1 安装宝塔面板

```bash
# CentOS 安装命令
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian 安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 1.2 登录宝塔面板

安装完成后，记录下面板地址、用户名和密码，通过浏览器访问面板。

### 1.3 安装必要软件

在宝塔面板中安装以下软件：
- **Nginx** 1.18+
- **MySQL** 8.0
- **Python项目管理器**
- **Node.js版本管理器**
- **PM2管理器**

## 🗄️ 第二步：数据库配置

### 2.1 创建数据库

1. 进入宝塔面板 → 数据库 → 添加数据库
2. 数据库名：`usdc_defi`
3. 用户名：`usdc_defi_user`
4. 密码：设置强密码
5. 访问权限：本地服务器

### 2.2 导入数据库结构

```bash
# 进入项目目录
cd /www/wwwroot/usdc-defi

# 导入数据库结构
mysql -u usdc_defi_user -p usdc_defi < database/schema.sql

# 导入初始数据
mysql -u usdc_defi_user -p usdc_defi < database/migrations/001_initial_data.sql

# 导入演示数据（可选）
mysql -u usdc_defi_user -p usdc_defi < database/seeds/demo_data.sql
```

### 2.3 数据库优化配置

编辑 MySQL 配置文件 `/etc/my.cnf`：

```ini
[mysqld]
# 基础配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 性能优化
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 连接配置
max_connections = 200
max_connect_errors = 1000
wait_timeout = 28800
interactive_timeout = 28800

# 查询缓存
query_cache_type = 1
query_cache_size = 128M
```

## 🐍 第三步：Python环境配置

### 3.1 创建Python虚拟环境

1. 进入宝塔面板 → Python项目管理器
2. 添加项目：
   - 项目名称：`usdc-defi`
   - Python版本：`3.8+`
   - 项目路径：`/www/wwwroot/usdc-defi/backend`
   - 启动文件：`main.py`

### 3.2 安装Python依赖

```bash
# 激活虚拟环境
source /www/server/pyproject_env/usdc-defi/bin/activate

# 进入后端目录
cd /www/wwwroot/usdc-defi/backend

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import fastapi; print('FastAPI installed successfully')"
```

### 3.3 配置环境变量

创建 `/www/wwwroot/usdc-defi/backend/.env` 文件：

```env
# 应用配置
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your_super_secret_key_here_change_in_production

# 服务器配置
HOST=127.0.0.1
PORT=8000
WORKERS=4

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=usdc_defi_user
DB_PASSWORD=your_secure_password
DB_NAME=usdc_defi

# 区块链配置
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_infura_project_id
USDC_CONTRACT_ADDRESS=******************************************
PLATFORM_CONTRACT_ADDRESS=******************************************
GAS_SPONSOR_WALLET=******************************************
GAS_SPONSOR_PRIVATE_KEY=your_gas_sponsor_private_key

# OpenZeppelin Defender
DEFENDER_API_KEY=your_defender_api_key
DEFENDER_API_SECRET=your_defender_api_secret
DEFENDER_RELAYER_ID=your_defender_relayer_id

# CORS配置
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/www/wwwlogs/usdc-defi/app.log
```

## 🌐 第四步：Node.js和前端配置

### 4.1 安装Node.js

1. 进入宝塔面板 → Node.js版本管理器
2. 安装 Node.js 16+ 版本
3. 设置为默认版本

### 4.2 构建前端应用

```bash
# 进入前端目录
cd /www/wwwroot/usdc-defi/frontend

# 安装依赖
npm install

# 创建环境变量文件
cp .env.example .env

# 编辑环境变量
nano .env
```

前端环境变量配置：

```env
REACT_APP_API_BASE_URL=https://your-domain.com/api/v1
REACT_APP_RAINBOWKIT_PROJECT_ID=1a54ba92caa7810745990910f7daccc4
REACT_APP_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_infura_project_id
REACT_APP_USDC_CONTRACT_ADDRESS=******************************************
REACT_APP_PLATFORM_CONTRACT_ADDRESS=******************************************
REACT_APP_GAS_SPONSOR_WALLET=******************************************
```

构建生产版本：

```bash
# 构建应用
npm run build

# 验证构建结果
ls -la build/
```

## ⚙️ 第五步：PM2配置

### 5.1 安装PM2

```bash
# 全局安装PM2
npm install -g pm2

# 验证安装
pm2 --version
```

### 5.2 配置PM2

将 `deployment/pm2/ecosystem.config.js` 复制到项目根目录：

```bash
cp deployment/pm2/ecosystem.config.js /www/wwwroot/usdc-defi/
```

编辑配置文件，更新相关路径和环境变量。

### 5.3 启动应用

```bash
# 进入项目目录
cd /www/wwwroot/usdc-defi

# 启动应用
pm2 start ecosystem.config.js --env production

# 查看状态
pm2 status

# 查看日志
pm2 logs

# 设置开机自启
pm2 startup
pm2 save
```

## 🌐 第六步：Nginx配置

### 6.1 添加网站

1. 进入宝塔面板 → 网站 → 添加站点
2. 域名：`your-domain.com`
3. 根目录：`/www/wwwroot/usdc-defi/frontend/build`
4. PHP版本：纯静态

### 6.2 配置Nginx

将 `deployment/nginx/usdc-defi.conf` 的内容复制到网站的Nginx配置中：

1. 进入网站设置 → 配置文件
2. 替换配置内容
3. 更新域名和路径
4. 保存并重载Nginx

### 6.3 SSL证书配置

1. 进入网站设置 → SSL
2. 选择Let's Encrypt或上传自有证书
3. 强制HTTPS
4. 开启HSTS

## 🔒 第七步：安全配置

### 7.1 防火墙设置

在宝塔面板 → 安全中配置：

```
开放端口：
- 22 (SSH)
- 80 (HTTP)
- 443 (HTTPS)
- 8888 (宝塔面板)

关闭端口：
- 8000 (后端API，仅内部访问)
- 3306 (MySQL，仅内部访问)
```

### 7.2 SSH安全

```bash
# 修改SSH端口
nano /etc/ssh/sshd_config

# 禁用root密码登录，使用密钥认证
PasswordAuthentication no
PubkeyAuthentication yes

# 重启SSH服务
systemctl restart sshd
```

### 7.3 文件权限

```bash
# 设置正确的文件权限
chown -R www:www /www/wwwroot/usdc-defi
chmod -R 755 /www/wwwroot/usdc-defi
chmod -R 644 /www/wwwroot/usdc-defi/frontend/build

# 保护敏感文件
chmod 600 /www/wwwroot/usdc-defi/backend/.env
```

## 📊 第八步：监控和日志

### 8.1 创建日志目录

```bash
mkdir -p /www/wwwlogs/usdc-defi
chown -R www:www /www/wwwlogs/usdc-defi
```

### 8.2 配置日志轮转

创建 `/etc/logrotate.d/usdc-defi`：

```
/www/wwwlogs/usdc-defi/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www www
    postrotate
        pm2 reload all
    endscript
}
```

### 8.3 监控脚本

创建监控脚本 `/www/wwwroot/usdc-defi/scripts/monitor.sh`：

```bash
#!/bin/bash

# 检查PM2进程
pm2 status | grep -q "online" || {
    echo "PM2 processes not running, restarting..."
    pm2 restart all
}

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is ${DISK_USAGE}%, cleaning logs..."
    find /www/wwwlogs -name "*.log" -mtime +7 -delete
fi

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "Memory usage is ${MEMORY_USAGE}%, restarting PM2..."
    pm2 restart all
fi
```

添加到crontab：

```bash
# 每5分钟检查一次
*/5 * * * * /www/wwwroot/usdc-defi/scripts/monitor.sh >> /www/wwwlogs/usdc-defi/monitor.log 2>&1
```

## 🔄 第九步：备份策略

### 9.1 数据库备份

在宝塔面板 → 计划任务中添加：

```bash
# 每日备份数据库
mysqldump -u usdc_defi_user -p'your_password' usdc_defi > /www/backup/usdc_defi_$(date +%Y%m%d).sql

# 保留30天备份
find /www/backup -name "usdc_defi_*.sql" -mtime +30 -delete
```

### 9.2 代码备份

```bash
# 每周备份代码
tar -czf /www/backup/usdc_defi_code_$(date +%Y%m%d).tar.gz /www/wwwroot/usdc-defi

# 保留4周备份
find /www/backup -name "usdc_defi_code_*.tar.gz" -mtime +28 -delete
```

## 🚀 第十步：部署验证

### 10.1 健康检查

```bash
# 检查后端API
curl -f http://localhost:8000/health

# 检查前端
curl -f https://your-domain.com/health

# 检查数据库连接
mysql -u usdc_defi_user -p usdc_defi -e "SELECT 1"
```

### 10.2 功能测试

1. 访问网站首页
2. 测试钱包连接
3. 检查实时数据滚动
4. 验证API响应

### 10.3 性能测试

```bash
# 使用ab进行简单压力测试
ab -n 1000 -c 10 https://your-domain.com/

# 检查响应时间
curl -w "@curl-format.txt" -o /dev/null -s https://your-domain.com/api/v1/demo/deposits
```

## 📝 维护指南

### 日常维护

1. **监控日志**: 定期检查错误日志
2. **性能监控**: 监控CPU、内存、磁盘使用
3. **安全更新**: 及时更新系统和软件包
4. **备份验证**: 定期验证备份完整性

### 故障排除

1. **应用无法启动**: 检查PM2日志和环境变量
2. **数据库连接失败**: 检查数据库服务和权限
3. **前端无法访问**: 检查Nginx配置和SSL证书
4. **API响应慢**: 检查数据库性能和网络连接

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 更新后端依赖
cd backend && pip install -r requirements.txt

# 重新构建前端
cd ../frontend && npm install && npm run build

# 重启服务
pm2 reload all
```

## 🎉 部署完成

恭喜！您已成功在宝塔面板上部署了USDC-DeFi挖矿平台。

### 重要提醒

1. **更改默认密码**: 修改所有默认密码
2. **配置监控**: 设置服务器和应用监控
3. **备份策略**: 确保备份策略正常运行
4. **安全审计**: 定期进行安全检查
5. **性能优化**: 根据实际使用情况优化配置

如有问题，请参考故障排除部分或联系技术支持。
