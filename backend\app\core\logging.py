"""
Logging configuration for the application
"""

import logging
import logging.config
import sys
from pathlib import Path
from typing import Dict, Any

from app.core.config import settings

def setup_logging() -> logging.Logger:
    """
    Setup application logging configuration
    
    Returns:
        Configured logger instance
    """
    
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Logging configuration
    logging_config: Dict[str, Any] = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s:%(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "json": {
                "format": '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d, "message": "%(message)s"}',
                "datefmt": "%Y-%m-%d %H:%M:%S"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "default",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "detailed",
                "filename": log_dir / "app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": log_dir / "error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8"
            },
            "auth_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": log_dir / "auth.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8"
            },
            "transaction_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": log_dir / "transactions.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8"
            }
        },
        "loggers": {
            "": {  # Root logger
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file", "error_file"]
            },
            "app.services.auth": {
                "level": "INFO",
                "handlers": ["auth_file"],
                "propagate": False
            },
            "app.services.transaction": {
                "level": "INFO",
                "handlers": ["transaction_file"],
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["console", "error_file"],
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False
            }
        }
    }
    
    # Apply logging configuration
    logging.config.dictConfig(logging_config)
    
    # Get main application logger
    logger = logging.getLogger("app")
    
    # Log startup message
    logger.info("🚀 USDC-DeFi Mining Platform logging initialized")
    logger.info(f"📊 Log level: {settings.LOG_LEVEL}")
    logger.info(f"📁 Log directory: {log_dir.absolute()}")
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for a specific module
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)

class LoggerMixin:
    """
    Mixin class to add logging capabilities to other classes
    """
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class"""
        return logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")

def log_function_call(func):
    """
    Decorator to log function calls
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} failed with error: {e}")
            raise
    
    return wrapper

def log_async_function_call(func):
    """
    Decorator to log async function calls
    
    Args:
        func: Async function to decorate
        
    Returns:
        Decorated async function
    """
    async def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"Calling async {func.__name__} with args={args}, kwargs={kwargs}")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"Async {func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"Async {func.__name__} failed with error: {e}")
            raise
    
    return wrapper

# Custom log filters
class SensitiveDataFilter(logging.Filter):
    """Filter to remove sensitive data from logs"""
    
    SENSITIVE_PATTERNS = [
        "password",
        "secret",
        "token",
        "key",
        "private",
        "signature"
    ]
    
    def filter(self, record):
        """Filter sensitive data from log records"""
        if hasattr(record, 'msg'):
            msg = str(record.msg)
            for pattern in self.SENSITIVE_PATTERNS:
                if pattern in msg.lower():
                    # Replace sensitive data with asterisks
                    record.msg = msg.replace(pattern, "*" * len(pattern))
        
        return True

# Performance logging
class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(f"performance.{name}")
    
    def log_execution_time(self, operation: str, duration: float, **kwargs):
        """Log execution time for an operation"""
        self.logger.info(
            f"Operation: {operation}, Duration: {duration:.4f}s, Details: {kwargs}"
        )
    
    def log_database_query(self, query: str, duration: float, rows: int = None):
        """Log database query performance"""
        details = {"query": query[:100], "duration": duration}
        if rows is not None:
            details["rows"] = rows
        
        self.logger.info(f"Database query executed: {details}")

# Security logging
class SecurityLogger:
    """Logger for security events"""
    
    def __init__(self):
        self.logger = logging.getLogger("security")
    
    def log_auth_attempt(self, wallet_address: str, success: bool, ip: str = None):
        """Log authentication attempt"""
        self.logger.info(
            f"Auth attempt - Wallet: {wallet_address}, Success: {success}, IP: {ip}"
        )
    
    def log_suspicious_activity(self, activity: str, details: dict):
        """Log suspicious activity"""
        self.logger.warning(f"Suspicious activity: {activity}, Details: {details}")
    
    def log_rate_limit_exceeded(self, identifier: str, action: str):
        """Log rate limit exceeded"""
        self.logger.warning(f"Rate limit exceeded - ID: {identifier}, Action: {action}")

# Initialize loggers
performance_logger = PerformanceLogger("app")
security_logger = SecurityLogger()
