"""
Database configuration and connection management
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from typing import Generator
import logging

from .config import settings

logger = logging.getLogger(__name__)

# Create SQLAlchemy engine with connection pooling
engine = create_engine(
    settings.database_url,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=settings.DEBUG,
    connect_args={
        "charset": "utf8mb4",
        "use_unicode": True,
        "autocommit": False
    }
)

# Create SessionLocal class
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# Create Base class for models
Base = declarative_base()

# Metadata for table operations
metadata = MetaData()

async def create_tables():
    """Create all database tables"""
    try:
        # Import all models to ensure they are registered
        from app.db import models
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("✓ Database tables created successfully")
        
    except Exception as e:
        logger.error(f"✗ Error creating database tables: {e}")
        raise

def get_db() -> Generator[Session, None, None]:
    """
    Dependency function to get database session
    Use this in FastAPI route dependencies
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

class DatabaseManager:
    """Database manager for advanced operations"""
    
    @staticmethod
    def get_session() -> Session:
        """Get a new database session"""
        return SessionLocal()
    
    @staticmethod
    def execute_raw_sql(sql: str, params: dict = None):
        """Execute raw SQL query"""
        with SessionLocal() as session:
            try:
                result = session.execute(sql, params or {})
                session.commit()
                return result
            except Exception as e:
                session.rollback()
                logger.error(f"Raw SQL execution error: {e}")
                raise
    
    @staticmethod
    def test_connection() -> bool:
        """Test database connection"""
        try:
            with SessionLocal() as session:
                session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    @staticmethod
    def get_table_info(table_name: str) -> dict:
        """Get information about a specific table"""
        try:
            with SessionLocal() as session:
                # Get table structure
                result = session.execute(f"DESCRIBE {table_name}")
                columns = result.fetchall()
                
                # Get row count
                count_result = session.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = count_result.scalar()
                
                return {
                    "table_name": table_name,
                    "columns": [
                        {
                            "field": col[0],
                            "type": col[1],
                            "null": col[2],
                            "key": col[3],
                            "default": col[4],
                            "extra": col[5]
                        }
                        for col in columns
                    ],
                    "row_count": row_count
                }
        except Exception as e:
            logger.error(f"Error getting table info for {table_name}: {e}")
            return None

# Database health check
def check_database_health() -> dict:
    """Check database health and return status"""
    try:
        db_manager = DatabaseManager()
        
        # Test connection
        connection_ok = db_manager.test_connection()
        
        if not connection_ok:
            return {
                "status": "unhealthy",
                "error": "Cannot connect to database"
            }
        
        # Get basic statistics
        with SessionLocal() as session:
            # Check if main tables exist and get counts
            tables_info = {}
            main_tables = ["users", "deposits", "earnings", "demo_deposits", "interest_tiers"]
            
            for table in main_tables:
                try:
                    result = session.execute(f"SELECT COUNT(*) FROM {table}")
                    count = result.scalar()
                    tables_info[table] = count
                except Exception as e:
                    tables_info[table] = f"Error: {str(e)}"
        
        return {
            "status": "healthy",
            "connection": "ok",
            "tables": tables_info,
            "engine_info": {
                "pool_size": engine.pool.size(),
                "checked_in": engine.pool.checkedin(),
                "checked_out": engine.pool.checkedout(),
                "overflow": engine.pool.overflow(),
            }
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
