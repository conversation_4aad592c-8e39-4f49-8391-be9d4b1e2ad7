-- USDC-DeFi Mining Platform Database Schema
-- MySQL 8.0 Compatible

CREATE DATABASE IF NOT EXISTS usdc_defi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE usdc_defi;

-- Users table - Store user wallet information
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    wallet_address VARCHAR(42) NOT NULL UNIQUE COMMENT 'Ethereum wallet address',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    total_deposited DECIMAL(20, 6) DEFAULT 0.000000 COMMENT 'Total USDC deposited',
    total_earned DECIMAL(20, 6) DEFAULT 0.000000 COMMENT 'Total USDC earned',
    
    INDEX idx_wallet_address (wallet_address),
    INDEX idx_created_at (created_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB COMMENT='User accounts and wallet information';

-- Interest rate tiers configuration
CREATE TABLE interest_tiers (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tier_name VARCHAR(50) NOT NULL,
    min_amount DECIMAL(20, 6) NOT NULL COMMENT 'Minimum USDC amount for this tier',
    max_amount DECIMAL(20, 6) NULL COMMENT 'Maximum USDC amount for this tier (NULL for unlimited)',
    min_rate DECIMAL(5, 4) NOT NULL COMMENT 'Minimum annual interest rate (e.g., 0.0130 for 1.3%)',
    max_rate DECIMAL(5, 4) NOT NULL COMMENT 'Maximum annual interest rate (e.g., 0.0160 for 1.6%)',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_amount_range (min_amount, max_amount),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB COMMENT='Interest rate tier configuration';

-- Deposits table - Store user deposit records
CREATE TABLE deposits (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    amount DECIMAL(20, 6) NOT NULL COMMENT 'USDC deposit amount',
    interest_tier_id INT UNSIGNED NOT NULL,
    interest_rate DECIMAL(5, 4) NOT NULL COMMENT 'Applied interest rate',
    transaction_hash VARCHAR(66) NULL COMMENT 'Ethereum transaction hash',
    status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (interest_tier_id) REFERENCES interest_tiers(id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_transaction_hash (transaction_hash)
) ENGINE=InnoDB COMMENT='User deposit records';

-- Earnings table - Store daily earnings records
CREATE TABLE earnings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    deposit_id BIGINT UNSIGNED NOT NULL,
    amount DECIMAL(20, 6) NOT NULL COMMENT 'Daily earning amount in USDC',
    earning_date DATE NOT NULL,
    interest_rate DECIMAL(5, 4) NOT NULL COMMENT 'Applied interest rate',
    status ENUM('pending', 'distributed', 'failed') DEFAULT 'pending',
    transaction_hash VARCHAR(66) NULL COMMENT 'Distribution transaction hash',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (deposit_id) REFERENCES deposits(id) ON DELETE CASCADE,
    UNIQUE KEY unique_daily_earning (user_id, deposit_id, earning_date),
    INDEX idx_user_id (user_id),
    INDEX idx_deposit_id (deposit_id),
    INDEX idx_earning_date (earning_date),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='Daily earnings records';

-- Withdrawals table - Store withdrawal requests
CREATE TABLE withdrawals (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    withdrawal_type ENUM('principal', 'earnings', 'both') NOT NULL,
    amount DECIMAL(20, 6) NOT NULL COMMENT 'Withdrawal amount in USDC',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    transaction_hash VARCHAR(66) NULL COMMENT 'Withdrawal transaction hash',
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_requested_at (requested_at)
) ENGINE=InnoDB COMMENT='Withdrawal requests and records';

-- Transactions table - Store all blockchain transactions
CREATE TABLE transactions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    transaction_hash VARCHAR(66) NOT NULL UNIQUE,
    transaction_type ENUM('deposit', 'withdrawal', 'earning_distribution') NOT NULL,
    amount DECIMAL(20, 6) NOT NULL COMMENT 'Transaction amount in USDC',
    gas_fee DECIMAL(20, 6) DEFAULT 0.000000 COMMENT 'Gas fee paid (should be 0 for meta-transactions)',
    block_number BIGINT UNSIGNED NULL,
    block_timestamp TIMESTAMP NULL,
    status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_hash (transaction_hash),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_status (status),
    INDEX idx_block_number (block_number)
) ENGINE=InnoDB COMMENT='Blockchain transaction records';

-- Demo deposits table - Store mock deposit data for homepage display
CREATE TABLE demo_deposits (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    wallet_address VARCHAR(42) NOT NULL COMMENT 'Anonymized wallet address for display',
    amount DECIMAL(20, 6) NOT NULL COMMENT 'Deposit amount in USDC',
    display_time VARCHAR(20) NOT NULL COMMENT 'Human readable time (e.g., "2 minutes ago")',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='Mock deposit data for homepage scrolling display';

-- System configuration table
CREATE TABLE system_config (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB COMMENT='System configuration parameters';
