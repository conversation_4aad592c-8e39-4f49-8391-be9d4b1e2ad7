# USDC-DeFi Frontend Environment Variables

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8000/api/v1
REACT_APP_WS_URL=ws://localhost:8000/ws

# RainbowKit Configuration
REACT_APP_RAINBOWKIT_PROJECT_ID=1a54ba92caa7810745990910f7daccc4

# Blockchain Configuration
REACT_APP_ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
REACT_APP_CHAIN_ID=1
REACT_APP_USDC_CONTRACT_ADDRESS=******************************************
REACT_APP_PLATFORM_CONTRACT_ADDRESS=******************************************

# Gas Sponsor Configuration
REACT_APP_GAS_SPONSOR_WALLET=******************************************

# Application Configuration
REACT_APP_APP_NAME=USDC-DeFi Mining Platform
REACT_APP_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Feature Flags
REACT_APP_ENABLE_DEMO_MODE=true
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_NOTIFICATIONS=true

# UI Configuration
REACT_APP_DEFAULT_LANGUAGE=en
REACT_APP_THEME_MODE=dark
REACT_APP_ANIMATION_ENABLED=true

# Platform Limits
REACT_APP_MIN_DEPOSIT=100
REACT_APP_MAX_DEPOSIT=10000000

# Demo Data Configuration
REACT_APP_DEMO_REFRESH_INTERVAL=30000
REACT_APP_DEMO_SCROLL_SPEED=50

# Analytics (Optional)
REACT_APP_GOOGLE_ANALYTICS_ID=
REACT_APP_MIXPANEL_TOKEN=

# Error Tracking (Optional)
REACT_APP_SENTRY_DSN=

# Social Links
REACT_APP_TWITTER_URL=https://twitter.com/usdcdefi
REACT_APP_TELEGRAM_URL=https://t.me/usdcdefi
REACT_APP_DISCORD_URL=https://discord.gg/usdcdefi
REACT_APP_GITHUB_URL=https://github.com/usdcdefi

# Documentation Links
REACT_APP_DOCS_URL=https://docs.usdcdefi.com
REACT_APP_WHITEPAPER_URL=https://docs.usdcdefi.com/whitepaper
REACT_APP_AUDIT_REPORT_URL=https://docs.usdcdefi.com/audit

# Support
REACT_APP_SUPPORT_EMAIL=<EMAIL>
REACT_APP_SUPPORT_CHAT_URL=https://chat.usdcdefi.com
