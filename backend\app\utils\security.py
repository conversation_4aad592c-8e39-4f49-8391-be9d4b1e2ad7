"""
Security utilities for authentication and encryption
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
import secrets
import hashlib

from app.core.config import settings

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token
    
    Args:
        data: Data to encode in token
        expires_delta: Token expiration time
        
    Returns:
        Encoded JWT token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "iat": datetime.utcnow()})
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    
    return encoded_jwt

def verify_token(token: str) -> Dict[str, Any]:
    """
    Verify and decode JWT token
    
    Args:
        token: JWT token to verify
        
    Returns:
        Decoded token payload
        
    Raises:
        JWTError: If token is invalid
    """
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        return payload
    except JWTError as e:
        raise JWTError(f"Token verification failed: {str(e)}")

def hash_password(password: str) -> str:
    """
    Hash password using bcrypt
    
    Args:
        password: Plain text password
        
    Returns:
        Hashed password
    """
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password against hash
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
        
    Returns:
        True if password matches
    """
    return pwd_context.verify(plain_password, hashed_password)

def generate_secure_token(length: int = 32) -> str:
    """
    Generate cryptographically secure random token
    
    Args:
        length: Token length in bytes
        
    Returns:
        Hex encoded secure token
    """
    return secrets.token_hex(length)

def generate_nonce() -> str:
    """
    Generate a unique nonce for authentication
    
    Returns:
        Unique nonce string
    """
    timestamp = int(datetime.utcnow().timestamp())
    random_part = secrets.token_hex(8)
    return f"{timestamp}_{random_part}"

def hash_data(data: str, salt: Optional[str] = None) -> str:
    """
    Hash data using SHA-256
    
    Args:
        data: Data to hash
        salt: Optional salt for hashing
        
    Returns:
        Hex encoded hash
    """
    if salt:
        data = f"{data}{salt}"
    
    return hashlib.sha256(data.encode()).hexdigest()

def verify_data_hash(data: str, hash_value: str, salt: Optional[str] = None) -> bool:
    """
    Verify data against its hash
    
    Args:
        data: Original data
        hash_value: Hash to verify against
        salt: Optional salt used in hashing
        
    Returns:
        True if data matches hash
    """
    computed_hash = hash_data(data, salt)
    return computed_hash == hash_value

def sanitize_wallet_address(address: str) -> str:
    """
    Sanitize and normalize wallet address
    
    Args:
        address: Wallet address to sanitize
        
    Returns:
        Sanitized wallet address
    """
    # Remove whitespace and convert to lowercase
    address = address.strip().lower()
    
    # Ensure it starts with 0x
    if not address.startswith('0x'):
        address = f"0x{address}"
    
    # Validate length
    if len(address) != 42:
        raise ValueError("Invalid wallet address length")
    
    # Validate hex characters
    try:
        int(address[2:], 16)
    except ValueError:
        raise ValueError("Invalid wallet address format")
    
    return address

def anonymize_wallet_address(address: str, show_chars: int = 4) -> str:
    """
    Anonymize wallet address for display
    
    Args:
        address: Wallet address to anonymize
        show_chars: Number of characters to show at start and end
        
    Returns:
        Anonymized wallet address
    """
    if len(address) < show_chars * 2 + 2:
        return address
    
    start = address[:show_chars + 2]  # Include 0x
    end = address[-show_chars:]
    
    return f"{start}***{end}"

def validate_signature_format(signature: str) -> bool:
    """
    Validate signature format
    
    Args:
        signature: Signature to validate
        
    Returns:
        True if signature format is valid
    """
    # Remove 0x prefix if present
    if signature.startswith('0x'):
        signature = signature[2:]
    
    # Check length (65 bytes = 130 hex characters)
    if len(signature) != 130:
        return False
    
    # Check if all characters are hex
    try:
        int(signature, 16)
        return True
    except ValueError:
        return False

def create_api_key() -> Dict[str, str]:
    """
    Create API key and secret pair
    
    Returns:
        Dictionary containing API key and secret
    """
    api_key = f"ak_{generate_secure_token(16)}"
    api_secret = generate_secure_token(32)
    
    return {
        "api_key": api_key,
        "api_secret": api_secret,
        "api_secret_hash": hash_password(api_secret)
    }

def verify_api_key(api_key: str, api_secret: str, stored_hash: str) -> bool:
    """
    Verify API key and secret
    
    Args:
        api_key: API key
        api_secret: API secret
        stored_hash: Stored hash of API secret
        
    Returns:
        True if API credentials are valid
    """
    # Verify API key format
    if not api_key.startswith('ak_'):
        return False
    
    # Verify API secret against stored hash
    return verify_password(api_secret, stored_hash)

def rate_limit_key(identifier: str, action: str, window: str = "hour") -> str:
    """
    Generate rate limit key for caching
    
    Args:
        identifier: Unique identifier (wallet address, IP, etc.)
        action: Action being rate limited
        window: Time window for rate limiting
        
    Returns:
        Rate limit cache key
    """
    timestamp = datetime.utcnow()
    
    if window == "minute":
        time_key = timestamp.strftime("%Y%m%d%H%M")
    elif window == "hour":
        time_key = timestamp.strftime("%Y%m%d%H")
    elif window == "day":
        time_key = timestamp.strftime("%Y%m%d")
    else:
        time_key = timestamp.strftime("%Y%m%d%H")
    
    return f"rate_limit:{identifier}:{action}:{time_key}"

def encrypt_sensitive_data(data: str, key: Optional[str] = None) -> str:
    """
    Encrypt sensitive data (placeholder implementation)
    
    Args:
        data: Data to encrypt
        key: Encryption key (uses default if not provided)
        
    Returns:
        Encrypted data
    """
    # TODO: Implement proper encryption using cryptography library
    # For now, just return base64 encoded data
    import base64
    return base64.b64encode(data.encode()).decode()

def decrypt_sensitive_data(encrypted_data: str, key: Optional[str] = None) -> str:
    """
    Decrypt sensitive data (placeholder implementation)
    
    Args:
        encrypted_data: Data to decrypt
        key: Decryption key (uses default if not provided)
        
    Returns:
        Decrypted data
    """
    # TODO: Implement proper decryption using cryptography library
    # For now, just return base64 decoded data
    import base64
    return base64.b64decode(encrypted_data.encode()).decode()
