"""
Web3 utilities for blockchain interactions
"""

from web3 import Web3
from eth_account.messages import encode_defunct
from typing import Optional, Dict, Any
import json
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

class Web3Service:
    """Service for Web3 and blockchain interactions"""
    
    def __init__(self):
        self.w3 = Web3(Web3.HTTPProvider(settings.ETHEREUM_RPC_URL))
        self.usdc_contract_address = settings.USDC_CONTRACT_ADDRESS
        self.platform_contract_address = settings.PLATFORM_CONTRACT_ADDRESS
        
        # Verify connection
        if not self.w3.is_connected():
            logger.error("Failed to connect to Ethereum RPC")
            raise Exception("Cannot connect to Ethereum network")
    
    async def verify_signature(
        self,
        wallet_address: str,
        message: str,
        signature: str
    ) -> bool:
        """
        Verify wallet signature for authentication
        
        Args:
            wallet_address: Ethereum wallet address
            message: Original message that was signed
            signature: Wallet signature
            
        Returns:
            True if signature is valid, False otherwise
        """
        try:
            # Encode the message
            encoded_message = encode_defunct(text=message)
            
            # Recover the address from signature
            recovered_address = self.w3.eth.account.recover_message(
                encoded_message,
                signature=signature
            )
            
            # Compare addresses (case insensitive)
            return recovered_address.lower() == wallet_address.lower()
            
        except Exception as e:
            logger.error(f"Signature verification error: {e}")
            return False
    
    async def get_usdc_balance(self, wallet_address: str) -> float:
        """
        Get USDC balance for a wallet address
        
        Args:
            wallet_address: Ethereum wallet address
            
        Returns:
            USDC balance as float
        """
        try:
            # USDC contract ABI (minimal for balance checking)
            usdc_abi = [
                {
                    "constant": True,
                    "inputs": [{"name": "_owner", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"name": "balance", "type": "uint256"}],
                    "type": "function"
                },
                {
                    "constant": True,
                    "inputs": [],
                    "name": "decimals",
                    "outputs": [{"name": "", "type": "uint8"}],
                    "type": "function"
                }
            ]
            
            # Create contract instance
            usdc_contract = self.w3.eth.contract(
                address=self.usdc_contract_address,
                abi=usdc_abi
            )
            
            # Get balance and decimals
            balance_wei = usdc_contract.functions.balanceOf(wallet_address).call()
            decimals = usdc_contract.functions.decimals().call()
            
            # Convert to human readable format
            balance = balance_wei / (10 ** decimals)
            
            return balance
            
        except Exception as e:
            logger.error(f"Error getting USDC balance for {wallet_address}: {e}")
            return 0.0
    
    async def get_transaction_status(self, tx_hash: str) -> Dict[str, Any]:
        """
        Get transaction status and details
        
        Args:
            tx_hash: Transaction hash
            
        Returns:
            Dictionary containing transaction details
        """
        try:
            # Get transaction receipt
            receipt = self.w3.eth.get_transaction_receipt(tx_hash)
            
            # Get transaction details
            transaction = self.w3.eth.get_transaction(tx_hash)
            
            # Get current block number
            current_block = self.w3.eth.block_number
            
            return {
                "hash": tx_hash,
                "status": "confirmed" if receipt.status == 1 else "failed",
                "block_number": receipt.blockNumber,
                "block_hash": receipt.blockHash.hex(),
                "gas_used": receipt.gasUsed,
                "gas_price": transaction.gasPrice,
                "confirmations": current_block - receipt.blockNumber,
                "from_address": transaction["from"],
                "to_address": transaction.to,
                "value": transaction.value
            }
            
        except Exception as e:
            logger.error(f"Error getting transaction status for {tx_hash}: {e}")
            return {
                "hash": tx_hash,
                "status": "pending",
                "error": str(e)
            }
    
    async def estimate_gas_fee(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Estimate gas fee for a transaction
        
        Args:
            transaction_data: Transaction data
            
        Returns:
            Dictionary containing gas estimates
        """
        try:
            # Get current gas price
            gas_price = self.w3.eth.gas_price
            
            # Estimate gas limit
            gas_limit = self.w3.eth.estimate_gas(transaction_data)
            
            # Calculate total fee
            total_fee_wei = gas_price * gas_limit
            total_fee_eth = self.w3.from_wei(total_fee_wei, 'ether')
            
            return {
                "gas_price": gas_price,
                "gas_limit": gas_limit,
                "total_fee_wei": total_fee_wei,
                "total_fee_eth": float(total_fee_eth),
                "total_fee_gwei": self.w3.from_wei(gas_price, 'gwei')
            }
            
        except Exception as e:
            logger.error(f"Error estimating gas fee: {e}")
            return {
                "error": str(e)
            }
    
    async def send_meta_transaction(
        self,
        user_address: str,
        function_data: str,
        signature: str
    ) -> str:
        """
        Send meta-transaction using OpenZeppelin Defender
        
        Args:
            user_address: User's wallet address
            function_data: Encoded function data
            signature: User's signature
            
        Returns:
            Transaction hash
        """
        try:
            # This would integrate with OpenZeppelin Defender
            # For now, we'll simulate the process
            
            # TODO: Implement actual OpenZeppelin Defender integration
            # This would involve:
            # 1. Preparing the meta-transaction data
            # 2. Sending to Defender Relayer
            # 3. Getting transaction hash back
            
            logger.info(f"Sending meta-transaction for user {user_address}")
            
            # Simulate transaction hash
            import secrets
            simulated_tx_hash = "0x" + secrets.token_hex(32)
            
            return simulated_tx_hash
            
        except Exception as e:
            logger.error(f"Error sending meta-transaction: {e}")
            raise
    
    async def verify_contract_interaction(
        self,
        tx_hash: str,
        expected_contract: str,
        expected_function: str
    ) -> bool:
        """
        Verify that a transaction interacted with expected contract and function
        
        Args:
            tx_hash: Transaction hash
            expected_contract: Expected contract address
            expected_function: Expected function signature
            
        Returns:
            True if transaction matches expectations
        """
        try:
            transaction = self.w3.eth.get_transaction(tx_hash)
            receipt = self.w3.eth.get_transaction_receipt(tx_hash)
            
            # Check if transaction was successful
            if receipt.status != 1:
                return False
            
            # Check contract address
            if transaction.to.lower() != expected_contract.lower():
                return False
            
            # Check function signature (first 4 bytes of input data)
            if len(transaction.input) >= 10:  # 0x + 8 hex chars
                function_sig = transaction.input[:10]
                # TODO: Implement function signature verification
                # This would require ABI parsing or known function signatures
            
            return True
            
        except Exception as e:
            logger.error(f"Error verifying contract interaction: {e}")
            return False
    
    def get_network_info(self) -> Dict[str, Any]:
        """
        Get current network information
        
        Returns:
            Dictionary containing network details
        """
        try:
            return {
                "connected": self.w3.is_connected(),
                "chain_id": self.w3.eth.chain_id,
                "block_number": self.w3.eth.block_number,
                "gas_price": self.w3.eth.gas_price,
                "gas_price_gwei": float(self.w3.from_wei(self.w3.eth.gas_price, 'gwei'))
            }
        except Exception as e:
            logger.error(f"Error getting network info: {e}")
            return {"connected": False, "error": str(e)}
