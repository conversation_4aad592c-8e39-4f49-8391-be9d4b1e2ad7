const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

// Contract addresses for different networks
const NETWORK_CONFIG = {
  mainnet: {
    usdc: "******************************************", // Real USDC address
    gasSponsor: "******************************************",
  },
  goerli: {
    usdc: "******************************************", // Goerli USDC
    gasSponsor: "******************************************",
  },
  sepolia: {
    usdc: "******************************************", // Sepolia USDC
    gasSponsor: "******************************************",
  },
  localhost: {
    usdc: null, // Will deploy mock USDC
    gasSponsor: null, // Will use deployer address
  },
  hardhat: {
    usdc: null, // Will deploy mock USDC
    gasSponsor: null, // Will use deployer address
  },
};

async function main() {
  console.log("🚀 Starting USDC-DeFi Platform deployment...");
  console.log(`📡 Network: ${network.name}`);
  
  const [deployer] = await ethers.getSigners();
  console.log(`👤 Deployer: ${deployer.address}`);
  
  const balance = await ethers.provider.getBalance(deployer.address);
  console.log(`💰 Deployer balance: ${ethers.formatEther(balance)} ETH`);

  const networkConfig = NETWORK_CONFIG[network.name] || NETWORK_CONFIG.localhost;
  let usdcAddress = networkConfig.usdc;
  let gasSponsorAddress = networkConfig.gasSponsor || deployer.address;
  
  // Deploy mock USDC if needed (for local/test networks)
  if (!usdcAddress) {
    console.log("\n📄 Deploying Mock USDC...");
    const MockUSDC = await ethers.getContractFactory("MockUSDC");
    const mockUSDC = await MockUSDC.deploy();
    await mockUSDC.waitForDeployment();
    usdcAddress = await mockUSDC.getAddress();
    console.log(`✅ Mock USDC deployed to: ${usdcAddress}`);
    
    // Mint some USDC to deployer for testing
    const mintAmount = ethers.parseUnits("1000000", 6); // 1M USDC
    await mockUSDC.mint(deployer.address, mintAmount);
    console.log(`💰 Minted ${ethers.formatUnits(mintAmount, 6)} USDC to deployer`);
  }

  // Deploy MinimalForwarder for meta-transactions
  console.log("\n📄 Deploying MinimalForwarder...");
  const MinimalForwarder = await ethers.getContractFactory("MinimalForwarder");
  const forwarder = await MinimalForwarder.deploy();
  await forwarder.waitForDeployment();
  const forwarderAddress = await forwarder.getAddress();
  console.log(`✅ MinimalForwarder deployed to: ${forwarderAddress}`);

  // Deploy USDCDeFiPlatform
  console.log("\n📄 Deploying USDCDeFiPlatform...");
  const USDCDeFiPlatform = await ethers.getContractFactory("USDCDeFiPlatform");
  const platform = await USDCDeFiPlatform.deploy(
    usdcAddress,
    forwarderAddress,
    gasSponsorAddress
  );
  await platform.waitForDeployment();
  const platformAddress = await platform.getAddress();
  console.log(`✅ USDCDeFiPlatform deployed to: ${platformAddress}`);

  // Verify deployment
  console.log("\n🔍 Verifying deployment...");
  
  // Check if contracts are deployed correctly
  const platformCode = await ethers.provider.getCode(platformAddress);
  const forwarderCode = await ethers.provider.getCode(forwarderAddress);
  
  if (platformCode === "0x") {
    throw new Error("Platform contract deployment failed");
  }
  if (forwarderCode === "0x") {
    throw new Error("Forwarder contract deployment failed");
  }
  
  console.log("✅ All contracts deployed successfully");

  // Test basic functionality
  console.log("\n🧪 Testing basic functionality...");
  
  try {
    // Test getting interest rate
    const testAmount = ethers.parseUnits("1000", 6); // 1000 USDC
    const interestRate = await platform.getInterestRate(testAmount);
    console.log(`📊 Interest rate for 1000 USDC: ${interestRate} basis points`);
    
    // Test getting tier count
    const tierCount = await platform.tierCount();
    console.log(`📈 Number of interest tiers: ${tierCount}`);
    
    // Test platform stats
    const totalValueLocked = await platform.totalValueLocked();
    console.log(`💎 Total Value Locked: ${ethers.formatUnits(totalValueLocked, 6)} USDC`);
    
    console.log("✅ Basic functionality tests passed");
  } catch (error) {
    console.error("❌ Basic functionality test failed:", error.message);
  }

  // Save deployment information
  const deploymentInfo = {
    network: network.name,
    chainId: network.config.chainId,
    deployer: deployer.address,
    timestamp: new Date().toISOString(),
    contracts: {
      USDCDeFiPlatform: {
        address: platformAddress,
        constructorArgs: [usdcAddress, forwarderAddress, gasSponsorAddress],
      },
      MinimalForwarder: {
        address: forwarderAddress,
        constructorArgs: [],
      },
      ...(networkConfig.usdc ? {} : {
        MockUSDC: {
          address: usdcAddress,
          constructorArgs: [],
        }
      }),
    },
    configuration: {
      usdcToken: usdcAddress,
      trustedForwarder: forwarderAddress,
      gasSponsor: gasSponsorAddress,
    },
  };

  // Create deployments directory if it doesn't exist
  const deploymentsDir = path.join(__dirname, "..", "deployments");
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
  }

  // Save deployment info to file
  const deploymentFile = path.join(deploymentsDir, `${network.name}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  console.log(`📝 Deployment info saved to: ${deploymentFile}`);

  // Generate environment variables for frontend/backend
  const envVars = `
# Generated deployment configuration for ${network.name}
REACT_APP_USDC_CONTRACT_ADDRESS=${usdcAddress}
REACT_APP_PLATFORM_CONTRACT_ADDRESS=${platformAddress}
REACT_APP_FORWARDER_CONTRACT_ADDRESS=${forwarderAddress}
REACT_APP_GAS_SPONSOR_WALLET=${gasSponsorAddress}

# Backend configuration
USDC_CONTRACT_ADDRESS=${usdcAddress}
PLATFORM_CONTRACT_ADDRESS=${platformAddress}
FORWARDER_CONTRACT_ADDRESS=${forwarderAddress}
GAS_SPONSOR_WALLET=${gasSponsorAddress}
`;

  const envFile = path.join(deploymentsDir, `${network.name}.env`);
  fs.writeFileSync(envFile, envVars.trim());
  console.log(`🔧 Environment variables saved to: ${envFile}`);

  // Print summary
  console.log("\n📋 Deployment Summary:");
  console.log("=" * 50);
  console.log(`Network: ${network.name}`);
  console.log(`Deployer: ${deployer.address}`);
  console.log(`USDC Token: ${usdcAddress}`);
  console.log(`MinimalForwarder: ${forwarderAddress}`);
  console.log(`USDCDeFiPlatform: ${platformAddress}`);
  console.log(`Gas Sponsor: ${gasSponsorAddress}`);
  console.log("=" * 50);

  // Verification instructions
  if (network.name !== "localhost" && network.name !== "hardhat") {
    console.log("\n📝 To verify contracts on Etherscan, run:");
    console.log(`npx hardhat verify --network ${network.name} ${forwarderAddress}`);
    console.log(`npx hardhat verify --network ${network.name} ${platformAddress} "${usdcAddress}" "${forwarderAddress}" "${gasSponsorAddress}"`);
  }

  console.log("\n🎉 Deployment completed successfully!");
}

// Error handling
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
