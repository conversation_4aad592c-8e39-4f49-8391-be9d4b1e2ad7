"""
Main API router that includes all route modules
"""

from fastapi import APIRouter

# Import all route modules
from .auth import router as auth_router
from .users import router as users_router
from .deposits import router as deposits_router
from .earnings import router as earnings_router
from .withdrawals import router as withdrawals_router
from .demo import router as demo_router
from .admin import router as admin_router
from .dashboard import router as dashboard_router

# Create main API router
api_router = APIRouter()

# Include all route modules with their prefixes
api_router.include_router(
    auth_router,
    prefix="/auth",
    tags=["Authentication"]
)

api_router.include_router(
    users_router,
    prefix="/users",
    tags=["Users"]
)

api_router.include_router(
    deposits_router,
    prefix="/deposits",
    tags=["Deposits"]
)

api_router.include_router(
    earnings_router,
    prefix="/earnings",
    tags=["Earnings"]
)

api_router.include_router(
    withdrawals_router,
    prefix="/withdrawals",
    tags=["Withdrawals"]
)

api_router.include_router(
    demo_router,
    prefix="/demo",
    tags=["Demo Data"]
)

api_router.include_router(
    dashboard_router,
    prefix="/dashboard",
    tags=["Dashboard"]
)

api_router.include_router(
    admin_router,
    prefix="/admin",
    tags=["Admin"]
)

# Health check endpoint for the API
@api_router.get("/health", summary="API Health Check")
async def api_health():
    """API health check endpoint"""
    return {
        "status": "healthy",
        "service": "usdc-defi-api",
        "version": "1.0.0"
    }
