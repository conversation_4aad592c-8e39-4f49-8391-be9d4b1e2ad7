/* USDC-DeFi Platform Global Styles */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
  background-color: #ffffff;
}

code {
  font-family: 'Fira Code', 'Monaco', 'Consolas', 'Ubuntu Mono', monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode scrollbar */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: #2a2a2a;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: #555;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Utility classes */
.text-gradient {
  background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(0, 102, 255, 0.3);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(0, 102, 255, 0.3);
  border-radius: 50%;
  border-top-color: #0066ff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 102, 255, 0.4);
}

.btn-secondary {
  background: transparent;
  border: 2px solid #0066ff;
  color: #0066ff;
  padding: 10px 22px;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #0066ff;
  color: white;
  transform: translateY(-2px);
}

/* Card styles */
.card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .card {
  background: #1a1a1a;
  border-color: #333;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

/* Typography */
.heading-xl {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.heading-lg {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.75rem;
}

.heading-md {
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.text-muted {
  color: #666;
}

[data-theme="dark"] .text-muted {
  color: #a3a3a3;
}

/* Responsive design */
@media (max-width: 768px) {
  .heading-xl {
    font-size: 2.5rem;
  }
  
  .heading-lg {
    font-size: 2rem;
  }
  
  .heading-md {
    font-size: 1.5rem;
  }
  
  .card {
    padding: 16px;
  }
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #0066ff;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid #000;
  }
  
  [data-theme="dark"] .card {
    border: 2px solid #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
