"""
Demo data service for managing mock deposit data
"""

import random
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from decimal import Decimal

from app.db.models import DemoDeposit

class DemoService:
    """Service for managing demo deposit data"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_demo_deposits(
        self,
        limit: int = 30,
        active_only: bool = True
    ) -> List[DemoDeposit]:
        """
        Get demo deposits for homepage display
        
        Args:
            limit: Maximum number of deposits to return
            active_only: Whether to return only active deposits
            
        Returns:
            List of demo deposit objects
        """
        query = self.db.query(DemoDeposit)
        
        if active_only:
            query = query.filter(DemoDeposit.is_active == True)
        
        # Order by created_at to simulate recent activity
        query = query.order_by(DemoDeposit.created_at.desc())
        
        return query.limit(limit).all()
    
    async def get_random_demo_deposits(self, count: int = 10) -> List[DemoDeposit]:
        """
        Get random demo deposits for varied display
        
        Args:
            count: Number of random deposits to return
            
        Returns:
            List of randomly selected demo deposits
        """
        # Get total count of active deposits
        total_count = self.db.query(DemoDeposit).filter(
            DemoDeposit.is_active == True
        ).count()
        
        if total_count == 0:
            return []
        
        # Generate random offsets
        random_offsets = random.sample(
            range(total_count),
            min(count, total_count)
        )
        
        deposits = []
        for offset in random_offsets:
            deposit = self.db.query(DemoDeposit).filter(
                DemoDeposit.is_active == True
            ).offset(offset).first()
            
            if deposit:
                deposits.append(deposit)
        
        return deposits
    
    async def get_recent_demo_deposits(
        self,
        minutes: int = 60,
        limit: int = 20
    ) -> List[DemoDeposit]:
        """
        Get demo deposits that appear to be recent
        
        Args:
            minutes: Consider deposits from last N minutes
            limit: Maximum number of deposits to return
            
        Returns:
            List of "recent" demo deposits
        """
        # Filter by display_time to simulate recent activity
        recent_time_patterns = [
            "minute ago", "minutes ago", "hour ago"
        ]
        
        query = self.db.query(DemoDeposit).filter(
            and_(
                DemoDeposit.is_active == True,
                func.lower(DemoDeposit.display_time).contains("ago")
            )
        )
        
        # If looking for very recent (< 60 minutes), filter for minute patterns
        if minutes <= 60:
            minute_filter = func.lower(DemoDeposit.display_time).contains("minute")
            query = query.filter(minute_filter)
        
        return query.order_by(DemoDeposit.created_at.desc()).limit(limit).all()
    
    async def get_demo_deposits_by_amount(
        self,
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None,
        limit: int = 20
    ) -> List[DemoDeposit]:
        """
        Get demo deposits filtered by amount range
        
        Args:
            min_amount: Minimum deposit amount
            max_amount: Maximum deposit amount
            limit: Maximum number of deposits to return
            
        Returns:
            List of demo deposits within amount range
        """
        query = self.db.query(DemoDeposit).filter(DemoDeposit.is_active == True)
        
        if min_amount is not None:
            query = query.filter(DemoDeposit.amount >= Decimal(str(min_amount)))
        
        if max_amount is not None:
            query = query.filter(DemoDeposit.amount <= Decimal(str(max_amount)))
        
        return query.order_by(DemoDeposit.amount.desc()).limit(limit).all()
    
    async def get_demo_stats(self) -> Dict[str, Any]:
        """
        Get statistics about demo deposit data
        
        Returns:
            Dictionary containing demo data statistics
        """
        # Basic counts
        total_count = self.db.query(DemoDeposit).count()
        active_count = self.db.query(DemoDeposit).filter(
            DemoDeposit.is_active == True
        ).count()
        
        # Amount statistics
        amount_stats = self.db.query(
            func.min(DemoDeposit.amount).label('min_amount'),
            func.max(DemoDeposit.amount).label('max_amount'),
            func.avg(DemoDeposit.amount).label('avg_amount'),
            func.sum(DemoDeposit.amount).label('total_amount')
        ).filter(DemoDeposit.is_active == True).first()
        
        # Time distribution
        time_patterns = self.db.query(
            DemoDeposit.display_time,
            func.count(DemoDeposit.id).label('count')
        ).filter(DemoDeposit.is_active == True).group_by(
            DemoDeposit.display_time
        ).all()
        
        return {
            "total_deposits": total_count,
            "active_deposits": active_count,
            "amount_stats": {
                "min_amount": float(amount_stats.min_amount) if amount_stats.min_amount else 0,
                "max_amount": float(amount_stats.max_amount) if amount_stats.max_amount else 0,
                "avg_amount": float(amount_stats.avg_amount) if amount_stats.avg_amount else 0,
                "total_amount": float(amount_stats.total_amount) if amount_stats.total_amount else 0
            },
            "time_distribution": [
                {"time": pattern.display_time, "count": pattern.count}
                for pattern in time_patterns
            ]
        }
    
    async def refresh_demo_timestamps(self) -> int:
        """
        Refresh demo data with new timestamps
        
        Returns:
            Number of records updated
        """
        # Generate new random timestamps
        time_patterns = [
            f"{random.randint(1, 10)} minutes ago",
            f"{random.randint(11, 30)} minutes ago",
            f"{random.randint(31, 60)} minutes ago",
            f"{random.randint(1, 2)} hour ago",
            f"{random.randint(2, 6)} hours ago"
        ]
        
        deposits = self.db.query(DemoDeposit).filter(
            DemoDeposit.is_active == True
        ).all()
        
        updated_count = 0
        for deposit in deposits:
            # Assign random time pattern
            deposit.display_time = random.choice(time_patterns)
            
            # Update created_at to simulate recent activity
            minutes_ago = random.randint(1, 360)  # 1-6 hours ago
            deposit.created_at = datetime.utcnow() - timedelta(minutes=minutes_ago)
            
            updated_count += 1
        
        self.db.commit()
        return updated_count
    
    async def get_formatted_activity_feed(self, count: int = 15) -> List[Dict[str, Any]]:
        """
        Get formatted activity feed for frontend display
        
        Args:
            count: Number of activities to return
            
        Returns:
            List of formatted activity objects
        """
        deposits = await self.get_demo_deposits(limit=count, active_only=True)
        
        activity_feed = []
        for deposit in deposits:
            # Format amount with commas
            formatted_amount = f"{deposit.amount:,.0f}"
            
            # Create activity object
            activity = {
                "id": deposit.id,
                "wallet_address": deposit.wallet_address,
                "amount": formatted_amount,
                "amount_raw": float(deposit.amount),
                "display_time": deposit.display_time,
                "created_at": deposit.created_at.isoformat(),
                "activity_type": "deposit",
                "formatted_text": f"{deposit.wallet_address} deposited {formatted_amount} USDC"
            }
            
            activity_feed.append(activity)
        
        return activity_feed
    
    def get_last_update_time(self) -> str:
        """
        Get the last update time for demo data
        
        Returns:
            ISO formatted timestamp of last update
        """
        latest_deposit = self.db.query(DemoDeposit).order_by(
            DemoDeposit.created_at.desc()
        ).first()
        
        if latest_deposit:
            return latest_deposit.created_at.isoformat()
        else:
            return datetime.utcnow().isoformat()
    
    async def create_demo_deposit(
        self,
        wallet_address: str,
        amount: Decimal,
        display_time: str
    ) -> DemoDeposit:
        """
        Create a new demo deposit record
        
        Args:
            wallet_address: Anonymized wallet address
            amount: Deposit amount
            display_time: Human readable time string
            
        Returns:
            Created demo deposit object
        """
        demo_deposit = DemoDeposit(
            wallet_address=wallet_address,
            amount=amount,
            display_time=display_time,
            is_active=True
        )
        
        self.db.add(demo_deposit)
        self.db.commit()
        self.db.refresh(demo_deposit)
        
        return demo_deposit
