import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  LinearProgress,
} from '@mui/material';
import { TrendingUp, Star } from '@mui/icons-material';
import { motion } from 'framer-motion';

const InterestTiersSection: React.FC = () => {
  const tiers = [
    {
      tier: 1,
      name: 'Starter',
      range: '100 - 4,999 USDC',
      minRate: 1.3,
      maxRate: 1.6,
      color: '#64748b',
      popular: false,
    },
    {
      tier: 2,
      name: 'Bronze',
      range: '5,000 - 19,999 USDC',
      minRate: 1.6,
      maxRate: 1.9,
      color: '#cd7f32',
      popular: false,
    },
    {
      tier: 3,
      name: 'Silver',
      range: '20,000 - 49,999 USDC',
      minRate: 1.9,
      maxRate: 2.2,
      color: '#c0c0c0',
      popular: true,
    },
    {
      tier: 4,
      name: 'Gold',
      range: '50,000 - 99,999 USDC',
      minRate: 2.2,
      maxRate: 2.5,
      color: '#ffd700',
      popular: false,
    },
    {
      tier: 5,
      name: 'Platinum',
      range: '100,000 - 199,999 USDC',
      minRate: 2.5,
      maxRate: 2.8,
      color: '#e5e4e2',
      popular: false,
    },
    {
      tier: 6,
      name: 'Diamond',
      range: '200,000 - 499,999 USDC',
      minRate: 2.8,
      maxRate: 3.1,
      color: '#b9f2ff',
      popular: false,
    },
    {
      tier: 7,
      name: 'Elite',
      range: '500,000 - 999,999 USDC',
      minRate: 3.1,
      maxRate: 3.5,
      color: '#9d4edd',
      popular: false,
    },
    {
      tier: 8,
      name: 'Premium',
      range: '1,000,000 - 1,999,999 USDC',
      minRate: 3.5,
      maxRate: 3.8,
      color: '#ff6b6b',
      popular: false,
    },
    {
      tier: 9,
      name: 'Whale',
      range: '2,000,000+ USDC',
      minRate: 4.1,
      maxRate: 4.1,
      color: '#4ecdc4',
      popular: false,
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <Box sx={{ py: 8, backgroundColor: 'background.default' }}>
      <Container maxWidth="lg">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Box textAlign="center" sx={{ mb: 6 }}>
            <Typography
              variant="h3"
              component="h2"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: 'linear-gradient(135deg, #0066ff 0%, #0052cc 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Tiered Interest Rates
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ maxWidth: 600, mx: 'auto', mb: 4 }}
            >
              Higher deposits unlock better rates. Start with as little as 100 USDC
              and earn up to 4.1% APY on larger deposits.
            </Typography>
          </Box>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <Grid container spacing={3}>
            {tiers.map((tier, index) => (
              <Grid item xs={12} sm={6} md={4} key={tier.tier}>
                <motion.div variants={itemVariants}>
                  <Card
                    sx={{
                      height: '100%',
                      position: 'relative',
                      border: tier.popular ? '2px solid #0066ff' : '1px solid',
                      borderColor: tier.popular ? '#0066ff' : 'divider',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: `0 12px 40px rgba(${tier.color.replace('#', '')}, 0.2)`,
                        borderColor: tier.color,
                      },
                    }}
                  >
                    {tier.popular && (
                      <Chip
                        label="Most Popular"
                        icon={<Star />}
                        sx={{
                          position: 'absolute',
                          top: -12,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          backgroundColor: '#0066ff',
                          color: 'white',
                          fontWeight: 600,
                        }}
                      />
                    )}

                    <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                      <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 700,
                            color: tier.color,
                            mb: 1,
                          }}
                        >
                          Tier {tier.tier} - {tier.name}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ mb: 2 }}
                        >
                          {tier.range}
                        </Typography>
                      </Box>

                      <Box sx={{ textAlign: 'center', mb: 3, flex: 1 }}>
                        <Typography
                          variant="h3"
                          sx={{
                            fontWeight: 700,
                            color: tier.color,
                            mb: 1,
                          }}
                        >
                          {tier.minRate === tier.maxRate 
                            ? `${tier.maxRate}%`
                            : `${tier.minRate}% - ${tier.maxRate}%`
                          }
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Annual Percentage Yield
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            Rate Progress
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {Math.round((tier.maxRate / 4.1) * 100)}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={(tier.maxRate / 4.1) * 100}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: 'action.hover',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: tier.color,
                              borderRadius: 4,
                            },
                          }}
                        />
                      </Box>

                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: 1,
                          color: tier.color,
                        }}
                      >
                        <TrendingUp fontSize="small" />
                        <Typography variant="body2" fontWeight={600}>
                          {tier.minRate === tier.maxRate 
                            ? 'Fixed Rate'
                            : 'Variable Rate'
                          }
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <Box
            sx={{
              mt: 6,
              p: 4,
              backgroundColor: 'action.hover',
              borderRadius: 2,
              textAlign: 'center',
            }}
          >
            <Typography variant="h6" gutterBottom>
              💡 Pro Tip
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Interest rates are calculated based on your total deposit amount across all positions.
              Larger deposits automatically qualify for higher tier rates with better yields.
            </Typography>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default InterestTiersSection;
