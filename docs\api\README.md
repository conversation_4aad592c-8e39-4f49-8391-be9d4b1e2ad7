# USDC-DeFi Platform API Documentation

## 📖 Overview

The USDC-DeFi Platform API provides a comprehensive set of endpoints for managing user authentication, deposits, earnings, withdrawals, and demo data. The API is built with FastAPI and supports wallet-based authentication with meta-transaction capabilities.

## 🔗 Base URL

```
Production: https://your-domain.com/api/v1
Development: http://localhost:8000/api/v1
```

## 🔐 Authentication

The API uses JWT (JSON Web Token) based authentication with wallet signatures. Users authenticate by signing a message with their Ethereum wallet.

### Authentication Flow

1. **Generate Message**: Get a message to sign
2. **Sign Message**: Sign the message with your wallet
3. **Login**: Submit the signature to get an access token
4. **Use Token**: Include the token in subsequent requests

### Headers

```http
Authorization: Bearer <your_jwt_token>
Content-Type: application/json
```

## 📚 API Endpoints

### Authentication Endpoints

#### Generate Authentication Message
```http
POST /auth/generate-message
```

**Request Body:**
```json
{
  "wallet_address": "******************************************"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Authentication message generated successfully",
  "data": {
    "message": "Welcome to USDC-DeFi Mining Platform!\n\nPlease sign this message...",
    "timestamp": 1703123456,
    "nonce": "1703123456_25a3b8D4",
    "wallet_address": "******************************************"
  }
}
```

#### Wallet Login
```http
POST /auth/wallet-login
```

**Request Body:**
```json
{
  "wallet_address": "******************************************",
  "signature": "0x1234567890abcdef...",
  "message": "Welcome to USDC-DeFi Mining Platform!...",
  "timestamp": 1703123456
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 604800,
  "user": {
    "id": 1,
    "wallet_address": "******************************************",
    "created_at": "2023-12-21T10:30:00Z",
    "total_deposited": "50000.000000",
    "total_earned": "1250.000000",
    "is_active": true
  }
}
```

#### Get Current User
```http
GET /auth/me
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": 1,
  "wallet_address": "******************************************",
  "created_at": "2023-12-21T10:30:00Z",
  "updated_at": "2023-12-21T15:45:00Z",
  "last_login_at": "2023-12-21T15:45:00Z",
  "is_active": true,
  "total_deposited": "50000.000000",
  "total_earned": "1250.000000"
}
```

### Demo Data Endpoints

#### Get Demo Deposits
```http
GET /demo/deposits?limit=30&active_only=true
```

**Query Parameters:**
- `limit` (integer, optional): Number of deposits to return (1-100, default: 30)
- `active_only` (boolean, optional): Return only active deposits (default: true)

**Response:**
```json
[
  {
    "id": 1,
    "wallet_address": "0x1a2b***3c4d",
    "amount": "1250.000000",
    "display_time": "2 minutes ago",
    "created_at": "2023-12-21T15:43:00Z",
    "is_active": true
  }
]
```

#### Get Activity Feed
```http
GET /demo/activity-feed?count=15
```

**Query Parameters:**
- `count` (integer, optional): Number of activities to return (1-50, default: 15)

**Response:**
```json
{
  "success": true,
  "message": "Activity feed retrieved successfully",
  "data": {
    "activities": [
      {
        "id": 1,
        "wallet_address": "0x1a2b***3c4d",
        "amount": "1,250",
        "amount_raw": 1250,
        "display_time": "2 minutes ago",
        "created_at": "2023-12-21T15:43:00Z",
        "activity_type": "deposit",
        "formatted_text": "0x1a2b***3c4d deposited 1,250 USDC"
      }
    ],
    "count": 15,
    "last_updated": "2023-12-21T15:45:00Z"
  }
}
```

#### Get Demo Statistics
```http
GET /demo/stats
```

**Response:**
```json
{
  "success": true,
  "message": "Demo statistics retrieved successfully",
  "data": {
    "total_deposits": 30,
    "active_deposits": 30,
    "amount_stats": {
      "min_amount": 1000.0,
      "max_amount": 2000000.0,
      "avg_amount": 125000.0,
      "total_amount": 3750000.0
    },
    "time_distribution": [
      {
        "time": "2 minutes ago",
        "count": 3
      }
    ]
  }
}
```

### User Endpoints

#### Get User Dashboard
```http
GET /users/dashboard
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "wallet_address": "******************************************",
    "total_deposited": "50000.000000",
    "total_earned": "1250.000000",
    "is_active": true
  },
  "active_deposits": [
    {
      "id": 1,
      "amount": "50000.000000",
      "interest_rate": "0.0235",
      "created_at": "2023-12-14T10:30:00Z",
      "status": "confirmed"
    }
  ],
  "recent_earnings": [
    {
      "id": 1,
      "amount": "3.424658",
      "earning_date": "2023-12-20",
      "status": "distributed"
    }
  ],
  "total_pending_earnings": "10.273974",
  "current_apy": "0.0235"
}
```

### Deposit Endpoints

#### Create Deposit
```http
POST /deposits
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "amount": 50000.0,
  "transaction_hash": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
}
```

**Response:**
```json
{
  "id": 1,
  "user_id": 1,
  "amount": "50000.000000",
  "interest_tier_id": 4,
  "interest_rate": "0.0235",
  "transaction_hash": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
  "status": "pending",
  "created_at": "2023-12-21T15:45:00Z",
  "updated_at": "2023-12-21T15:45:00Z"
}
```

#### Get User Deposits
```http
GET /deposits?page=1&size=10
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (integer, optional): Page number (default: 1)
- `size` (integer, optional): Items per page (default: 10)

**Response:**
```json
{
  "items": [
    {
      "id": 1,
      "amount": "50000.000000",
      "interest_rate": "0.0235",
      "status": "confirmed",
      "created_at": "2023-12-14T10:30:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10,
  "pages": 1
}
```

### Earnings Endpoints

#### Get User Earnings
```http
GET /earnings?page=1&size=10&date_from=2023-12-01&date_to=2023-12-31
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (integer, optional): Page number (default: 1)
- `size` (integer, optional): Items per page (default: 10)
- `date_from` (string, optional): Start date (YYYY-MM-DD)
- `date_to` (string, optional): End date (YYYY-MM-DD)

**Response:**
```json
{
  "items": [
    {
      "id": 1,
      "deposit_id": 1,
      "amount": "3.424658",
      "earning_date": "2023-12-20",
      "interest_rate": "0.0235",
      "status": "distributed",
      "transaction_hash": "0x7890123456789012345678901234567890123456789012345678901234567890",
      "created_at": "2023-12-21T00:00:00Z"
    }
  ],
  "total": 7,
  "page": 1,
  "size": 10,
  "pages": 1
}
```

#### Get Total Earnings
```http
GET /earnings/total
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response:**
```json
{
  "total_earned": "23.972606",
  "total_pending": "10.273974",
  "total_distributed": "13.698632",
  "earnings_count": 7
}
```

### Withdrawal Endpoints

#### Create Withdrawal
```http
POST /withdrawals
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "withdrawal_type": "both",
  "amount": 55000.0
}
```

**Response:**
```json
{
  "id": 1,
  "user_id": 1,
  "withdrawal_type": "both",
  "amount": "55000.000000",
  "status": "pending",
  "requested_at": "2023-12-21T15:45:00Z"
}
```

### Dashboard Endpoints

#### Get Platform Statistics
```http
GET /dashboard/stats
```

**Response:**
```json
{
  "total_users": 1250,
  "total_deposits": "15750000.000000",
  "total_earnings_distributed": "425000.000000",
  "active_deposits": 3420,
  "platform_tvl": "15750000.000000"
}
```

## 📊 Response Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 422 | Validation Error |
| 429 | Rate Limited |
| 500 | Internal Server Error |

## 🚫 Error Responses

```json
{
  "success": false,
  "message": "Error description",
  "detail": "Detailed error information"
}
```

## 📝 Rate Limiting

- **General API**: 30 requests per minute
- **Authentication**: 5 requests per minute
- **Other endpoints**: 10 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 30
X-RateLimit-Remaining: 29
X-RateLimit-Reset: 1703123516
```

## 🔧 SDK Examples

### JavaScript/TypeScript

```javascript
// Initialize API client
const apiClient = axios.create({
  baseURL: 'https://your-domain.com/api/v1',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth token to requests
apiClient.interceptors.request.use(config => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Wallet login example
async function walletLogin(walletAddress, signature, message, timestamp) {
  try {
    const response = await apiClient.post('/auth/wallet-login', {
      wallet_address: walletAddress,
      signature,
      message,
      timestamp
    });
    
    localStorage.setItem('access_token', response.data.access_token);
    return response.data;
  } catch (error) {
    console.error('Login failed:', error.response.data);
    throw error;
  }
}

// Get demo data
async function getDemoData() {
  try {
    const response = await apiClient.get('/demo/activity-feed?count=20');
    return response.data.data.activities;
  } catch (error) {
    console.error('Failed to fetch demo data:', error);
    throw error;
  }
}
```

### Python

```python
import requests
import json

class USDCDeFiAPI:
    def __init__(self, base_url, token=None):
        self.base_url = base_url
        self.session = requests.Session()
        if token:
            self.session.headers.update({'Authorization': f'Bearer {token}'})
    
    def wallet_login(self, wallet_address, signature, message, timestamp):
        data = {
            'wallet_address': wallet_address,
            'signature': signature,
            'message': message,
            'timestamp': timestamp
        }
        response = self.session.post(f'{self.base_url}/auth/wallet-login', json=data)
        response.raise_for_status()
        return response.json()
    
    def get_demo_data(self, count=15):
        response = self.session.get(f'{self.base_url}/demo/activity-feed?count={count}')
        response.raise_for_status()
        return response.json()['data']['activities']

# Usage
api = USDCDeFiAPI('https://your-domain.com/api/v1')
demo_data = api.get_demo_data(20)
```

## 🔍 Testing

The API includes interactive documentation available at:
- **Swagger UI**: `/api/docs`
- **ReDoc**: `/api/redoc`

For testing, you can use the provided Postman collection or the interactive docs.

## 📞 Support

For API support and questions:
- **Documentation**: [https://docs.your-domain.com](https://docs.your-domain.com)
- **Email**: <EMAIL>
- **Discord**: [https://discord.gg/your-server](https://discord.gg/your-server)
