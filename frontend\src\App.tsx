import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Box, CircularProgress } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';

// Layout components
import Layout from './components/Layout/Layout';
import LoadingScreen from './components/Common/LoadingScreen';
import ErrorBoundary from './components/Common/ErrorBoundary';

// Page components (lazy loaded)
const HomePage = React.lazy(() => import('./pages/HomePage'));
const DashboardPage = React.lazy(() => import('./pages/DashboardPage'));
const DepositPage = React.lazy(() => import('./pages/DepositPage'));
const WithdrawPage = React.lazy(() => import('./pages/WithdrawPage'));
const EarningsPage = React.lazy(() => import('./pages/EarningsPage'));
const HelpPage = React.lazy(() => import('./pages/HelpPage'));
const WhitepaperPage = React.lazy(() => import('./pages/WhitepaperPage'));
const AuditPage = React.lazy(() => import('./pages/AuditPage'));
const SupportPage = React.lazy(() => import('./pages/SupportPage'));
const NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'));

// Hooks
import { useAuth } from './hooks/useAuth';
import { useTheme } from './hooks/useTheme';

// Types
interface ProtectedRouteProps {
  children: React.ReactNode;
}

// Protected route component
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

// Page transition variants
const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  in: {
    opacity: 1,
    y: 0,
  },
  out: {
    opacity: 0,
    y: -20,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3,
};

// Suspense fallback component
const SuspenseFallback: React.FC = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    minHeight="60vh"
  >
    <CircularProgress size={40} />
  </Box>
);

const App: React.FC = () => {
  const { isDarkMode } = useTheme();

  return (
    <ErrorBoundary>
      <Helmet>
        <title>{process.env.REACT_APP_APP_NAME || 'USDC-DeFi Mining Platform'}</title>
        <meta 
          name="description" 
          content="Earn competitive yields on your USDC deposits with our secure DeFi mining platform. Tiered interest rates up to 4.1% APY with gas-free transactions." 
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content={isDarkMode ? "#000000" : "#ffffff"} />
        
        {/* Open Graph tags */}
        <meta property="og:title" content="USDC-DeFi Mining Platform" />
        <meta property="og:description" content="Earn competitive yields on your USDC deposits with our secure DeFi mining platform." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={window.location.origin} />
        
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="USDC-DeFi Mining Platform" />
        <meta name="twitter:description" content="Earn competitive yields on your USDC deposits with our secure DeFi mining platform." />
        
        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/logo192.png" />
        <link rel="manifest" href="/manifest.json" />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Google Fonts */}
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </Helmet>

      <Layout>
        <AnimatePresence mode="wait">
          <Suspense fallback={<SuspenseFallback />}>
            <Routes>
              {/* Public routes */}
              <Route
                path="/"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <HomePage />
                  </motion.div>
                }
              />
              
              <Route
                path="/whitepaper"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <WhitepaperPage />
                  </motion.div>
                }
              />
              
              <Route
                path="/audit"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <AuditPage />
                  </motion.div>
                }
              />
              
              <Route
                path="/support"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <SupportPage />
                  </motion.div>
                }
              />
              
              <Route
                path="/help"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <HelpPage />
                  </motion.div>
                }
              />

              {/* Protected routes */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <DashboardPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />
              
              <Route
                path="/deposit"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <DepositPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />
              
              <Route
                path="/withdraw"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <WithdrawPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />
              
              <Route
                path="/earnings"
                element={
                  <ProtectedRoute>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <EarningsPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 404 route */}
              <Route
                path="*"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <NotFoundPage />
                  </motion.div>
                }
              />
            </Routes>
          </Suspense>
        </AnimatePresence>
      </Layout>
    </ErrorBoundary>
  );
};

export default App;
