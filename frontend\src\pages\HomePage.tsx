import React from 'react';
import { <PERSON>, Container, <PERSON>po<PERSON>, <PERSON>ton, Grid, Card, CardContent } from '@mui/material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { ConnectButton } from '@rainbow-me/rainbowkit';

// Components
import HeroSection from '../components/Home/HeroSection';
import InterestTiersSection from '../components/Home/InterestTiersSection';
import LiveActivityFeed from '../components/Home/LiveActivityFeed';
import FeaturesSection from '../components/Home/FeaturesSection';
import StatsSection from '../components/Home/StatsSection';
import SecuritySection from '../components/Home/SecuritySection';
import FAQSection from '../components/Home/FAQSection';

// Hooks
import { useAuth } from '../hooks/useAuth';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
};

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const handleGetStarted = () => {
    if (isAuthenticated) {
      navigate('/dashboard');
    } else {
      // Scroll to connect wallet section or trigger wallet connection
      document.getElementById('connect-section')?.scrollIntoView({ 
        behavior: 'smooth' 
      });
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Hero Section */}
      <motion.div variants={itemVariants}>
        <HeroSection onGetStarted={handleGetStarted} />
      </motion.div>

      {/* Live Activity Feed */}
      <motion.div variants={itemVariants}>
        <Box sx={{ py: 8, backgroundColor: 'background.paper' }}>
          <Container maxWidth="lg">
            <Typography
              variant="h3"
              component="h2"
              textAlign="center"
              gutterBottom
              sx={{ mb: 6 }}
            >
              Live Platform Activity
            </Typography>
            <LiveActivityFeed />
          </Container>
        </Box>
      </motion.div>

      {/* Interest Tiers Section */}
      <motion.div variants={itemVariants}>
        <InterestTiersSection />
      </motion.div>

      {/* Platform Statistics */}
      <motion.div variants={itemVariants}>
        <StatsSection />
      </motion.div>

      {/* Features Section */}
      <motion.div variants={itemVariants}>
        <FeaturesSection />
      </motion.div>

      {/* Security Section */}
      <motion.div variants={itemVariants}>
        <SecuritySection />
      </motion.div>

      {/* Connect Wallet Section */}
      <motion.div variants={itemVariants}>
        <Box
          id="connect-section"
          sx={{
            py: 8,
            background: 'linear-gradient(135deg, #0066ff 0%, #0052cc 100%)',
            color: 'white',
          }}
        >
          <Container maxWidth="md">
            <Box textAlign="center">
              <Typography variant="h3" component="h2" gutterBottom>
                Ready to Start Earning?
              </Typography>
              <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                Connect your wallet to begin earning competitive yields on your USDC deposits
              </Typography>
              
              {!isAuthenticated ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
                  <ConnectButton />
                </Box>
              ) : (
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => navigate('/dashboard')}
                  sx={{
                    backgroundColor: 'white',
                    color: 'primary.main',
                    px: 4,
                    py: 2,
                    fontSize: '1.1rem',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    },
                  }}
                >
                  Go to Dashboard
                </Button>
              )}

              <Grid container spacing={3} sx={{ mt: 4 }}>
                <Grid item xs={12} md={4}>
                  <Card sx={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', color: 'white' }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" component="div" gutterBottom>
                        Gas-Free
                      </Typography>
                      <Typography variant="body2">
                        All transactions are sponsored by our platform
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', color: 'white' }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" component="div" gutterBottom>
                        Secure
                      </Typography>
                      <Typography variant="body2">
                        Audited smart contracts and proven security
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', color: 'white' }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" component="div" gutterBottom>
                        High Yield
                      </Typography>
                      <Typography variant="body2">
                        Up to 4.1% APY on your USDC deposits
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Container>
        </Box>
      </motion.div>

      {/* FAQ Section */}
      <motion.div variants={itemVariants}>
        <FAQSection />
      </motion.div>

      {/* Footer CTA */}
      <motion.div variants={itemVariants}>
        <Box sx={{ py: 6, backgroundColor: 'background.paper' }}>
          <Container maxWidth="md">
            <Box textAlign="center">
              <Typography variant="h4" component="h2" gutterBottom>
                Join the Future of DeFi
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, color: 'text.secondary' }}>
                Start earning competitive yields on your USDC with our secure, 
                gas-free mining platform today.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleGetStarted}
                  sx={{ px: 4 }}
                >
                  Get Started
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => navigate('/whitepaper')}
                  sx={{ px: 4 }}
                >
                  Read Whitepaper
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => navigate('/audit')}
                  sx={{ px: 4 }}
                >
                  View Audit
                </Button>
              </Box>
            </Box>
          </Container>
        </Box>
      </motion.div>
    </motion.div>
  );
};

export default HomePage;
