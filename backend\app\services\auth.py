"""
Authentication service for wallet-based login
"""

import time
from datetime import datetime, timedelta
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import JW<PERSON>rror, jwt

from app.core.config import settings
from app.core.database import get_db
from app.db.models import User
from app.utils.web3 import Web3Service
from app.utils.security import verify_token

security = HTTPBearer()

class AuthService:
    """Authentication service for wallet-based authentication"""
    
    def __init__(self):
        self.web3_service = Web3Service()
    
    async def generate_auth_message(self, wallet_address: str) -> dict:
        """
        Generate authentication message for wallet signature
        
        Args:
            wallet_address: Ethereum wallet address
            
        Returns:
            Dictionary containing message and timestamp
        """
        timestamp = int(time.time())
        nonce = f"{timestamp}_{wallet_address[-8:]}"
        
        message = (
            f"Welcome to USDC-DeFi Mining Platform!\n\n"
            f"Please sign this message to authenticate your wallet.\n\n"
            f"Wallet: {wallet_address}\n"
            f"Timestamp: {timestamp}\n"
            f"Nonce: {nonce}\n\n"
            f"This request will not trigger any blockchain transaction or cost any gas fees."
        )
        
        return {
            "message": message,
            "timestamp": timestamp,
            "nonce": nonce,
            "wallet_address": wallet_address
        }
    
    async def verify_wallet_signature(
        self,
        wallet_address: str,
        signature: str,
        message: str,
        timestamp: int
    ) -> bool:
        """
        Verify wallet signature for authentication
        
        Args:
            wallet_address: Ethereum wallet address
            signature: Wallet signature
            message: Original message that was signed
            timestamp: Timestamp when signature was created
            
        Returns:
            True if signature is valid, False otherwise
        """
        try:
            # Check timestamp validity (signature should not be older than 10 minutes)
            current_time = int(time.time())
            if current_time - timestamp > 600:  # 10 minutes
                return False
            
            # Verify the signature using Web3
            is_valid = await self.web3_service.verify_signature(
                wallet_address=wallet_address,
                message=message,
                signature=signature
            )
            
            return is_valid
            
        except Exception as e:
            print(f"Signature verification error: {e}")
            return False
    
    @staticmethod
    async def get_current_user(
        credentials: HTTPAuthorizationCredentials = Depends(security),
        db: Session = Depends(get_db)
    ) -> User:
        """
        Get current authenticated user from JWT token
        
        Args:
            credentials: HTTP authorization credentials
            db: Database session
            
        Returns:
            Current user object
            
        Raises:
            HTTPException: If token is invalid or user not found
        """
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
        try:
            # Verify and decode JWT token
            payload = verify_token(credentials.credentials)
            user_id: str = payload.get("sub")
            wallet_address: str = payload.get("wallet")
            
            if user_id is None or wallet_address is None:
                raise credentials_exception
                
        except JWTError:
            raise credentials_exception
        
        # Get user from database
        user = db.query(User).filter(User.id == int(user_id)).first()
        if user is None:
            raise credentials_exception
        
        # Verify wallet address matches
        if user.wallet_address.lower() != wallet_address.lower():
            raise credentials_exception
        
        # Check if user is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is inactive"
            )
        
        return user
    
    @staticmethod
    async def get_optional_user(
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
        db: Session = Depends(get_db)
    ) -> Optional[User]:
        """
        Get current user if authenticated, otherwise return None
        
        This is useful for endpoints that work for both authenticated and
        unauthenticated users.
        
        Args:
            credentials: HTTP authorization credentials (optional)
            db: Database session
            
        Returns:
            Current user object or None
        """
        if not credentials:
            return None
        
        try:
            return await AuthService.get_current_user(credentials, db)
        except HTTPException:
            return None
    
    async def validate_wallet_access(
        self,
        wallet_address: str,
        current_user: User
    ) -> bool:
        """
        Validate that the current user has access to the specified wallet
        
        Args:
            wallet_address: Wallet address to validate
            current_user: Current authenticated user
            
        Returns:
            True if user has access, False otherwise
        """
        return current_user.wallet_address.lower() == wallet_address.lower()
    
    async def check_rate_limit(
        self,
        wallet_address: str,
        action: str,
        limit_per_hour: int = 10
    ) -> bool:
        """
        Check rate limiting for wallet-based actions
        
        Args:
            wallet_address: Wallet address
            action: Action being performed
            limit_per_hour: Maximum actions per hour
            
        Returns:
            True if within rate limit, False otherwise
        """
        # This would typically use Redis or another cache
        # For now, we'll implement a simple in-memory rate limiter
        # In production, implement proper rate limiting with Redis
        
        # TODO: Implement proper rate limiting with Redis
        return True
    
    async def log_auth_attempt(
        self,
        wallet_address: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """
        Log authentication attempts for security monitoring
        
        Args:
            wallet_address: Wallet address
            success: Whether authentication was successful
            ip_address: Client IP address
            user_agent: Client user agent
        """
        # TODO: Implement authentication logging
        # This could log to database, file, or external service
        log_entry = {
            "timestamp": datetime.utcnow(),
            "wallet_address": wallet_address,
            "success": success,
            "ip_address": ip_address,
            "user_agent": user_agent
        }
        
        # For now, just print to console
        print(f"Auth attempt: {log_entry}")
    
    async def revoke_user_tokens(self, user_id: int):
        """
        Revoke all tokens for a user
        
        Since we're using stateless JWT tokens, this would typically
        involve adding the user to a blacklist or changing their secret.
        
        Args:
            user_id: User ID to revoke tokens for
        """
        # TODO: Implement token revocation
        # This could involve:
        # 1. Adding user to blacklist in Redis
        # 2. Changing user's secret key
        # 3. Updating token version in database
        pass
