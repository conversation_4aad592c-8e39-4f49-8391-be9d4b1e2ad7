import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  But<PERSON>,
  Grid,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import {
  TrendingUp,
  Security,
  Speed,
  AccountBalance,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import CountUp from 'react-countup';

interface HeroSectionProps {
  onGetStarted: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {
  const features = [
    {
      icon: <TrendingUp />,
      title: 'Up to 4.1% APY',
      description: 'Competitive yields with tiered interest rates',
      color: '#10b981',
    },
    {
      icon: <Speed />,
      title: 'Gas-Free',
      description: 'All transaction fees sponsored by platform',
      color: '#3b82f6',
    },
    {
      icon: <Security />,
      title: 'Secure & Audited',
      description: 'Smart contracts professionally audited',
      color: '#f59e0b',
    },
    {
      icon: <AccountBalance />,
      title: 'USDC Deposits',
      description: 'Stable coin deposits with instant liquidity',
      color: '#ef4444',
    },
  ];

  const stats = [
    { label: 'Total Value Locked', value: ********, prefix: '$', suffix: '' },
    { label: 'Active Users', value: 1250, prefix: '', suffix: '+' },
    { label: 'Total Earnings Paid', value: 425000, prefix: '$', suffix: '' },
    { label: 'Platform Uptime', value: 99.9, prefix: '', suffix: '%' },
  ];

  return (
    <Box
      sx={{
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
        color: 'white',
        py: { xs: 8, md: 12 },
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background Effects */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 50%, rgba(0, 102, 255, 0.1) 0%, transparent 50%)',
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)',
        }}
      />

      <Container maxWidth="lg">
        <Grid container spacing={6} alignItems="center">
          {/* Left Content */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Chip
                label="🚀 Now Live on Ethereum Mainnet"
                sx={{
                  mb: 3,
                  backgroundColor: 'rgba(0, 102, 255, 0.2)',
                  color: '#60a5fa',
                  border: '1px solid rgba(0, 102, 255, 0.3)',
                }}
              />

              <Typography
                variant="h2"
                component="h1"
                sx={{
                  fontWeight: 700,
                  mb: 3,
                  background: 'linear-gradient(135deg, #ffffff 0%, #60a5fa 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontSize: { xs: '2.5rem', md: '3.5rem' },
                  lineHeight: 1.2,
                }}
              >
                Earn Premium Yields on Your USDC
              </Typography>

              <Typography
                variant="h5"
                sx={{
                  mb: 4,
                  color: 'rgba(255, 255, 255, 0.8)',
                  fontWeight: 300,
                  lineHeight: 1.6,
                }}
              >
                Secure DeFi mining platform with tiered interest rates up to{' '}
                <span style={{ color: '#10b981', fontWeight: 600 }}>4.1% APY</span>.
                Gas-free transactions and instant liquidity.
              </Typography>

              <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={onGetStarted}
                  sx={{
                    background: 'linear-gradient(135deg, #0066ff 0%, #0052cc 100%)',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    '&:hover': {
                      background: 'linear-gradient(135deg, #0052cc 0%, #003d99 100%)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 8px 25px rgba(0, 102, 255, 0.4)',
                    },
                  }}
                >
                  Start Earning Now
                </Button>

                <Button
                  variant="outlined"
                  size="large"
                  sx={{
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    color: 'white',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    '&:hover': {
                      borderColor: 'white',
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    },
                  }}
                >
                  View Whitepaper
                </Button>
              </Box>

              {/* Stats */}
              <Grid container spacing={3}>
                {stats.map((stat, index) => (
                  <Grid item xs={6} sm={3} key={stat.label}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                    >
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 700,
                          color: '#10b981',
                          mb: 0.5,
                        }}
                      >
                        {stat.prefix}
                        <CountUp
                          end={stat.value}
                          duration={2}
                          separator=","
                          decimals={stat.label.includes('Uptime') ? 1 : 0}
                        />
                        {stat.suffix}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.7)',
                          fontSize: '0.875rem',
                        }}
                      >
                        {stat.label}
                      </Typography>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          </Grid>

          {/* Right Content - Features */}
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Grid container spacing={3}>
                {features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={feature.title}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                      whileHover={{ scale: 1.05 }}
                    >
                      <Card
                        sx={{
                          background: 'rgba(255, 255, 255, 0.05)',
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255, 255, 255, 0.1)',
                          color: 'white',
                          height: '100%',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'rgba(255, 255, 255, 0.1)',
                            border: `1px solid ${feature.color}`,
                            boxShadow: `0 8px 32px rgba(${feature.color.replace('#', '')}, 0.3)`,
                          },
                        }}
                      >
                        <CardContent sx={{ p: 3 }}>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              mb: 2,
                              color: feature.color,
                            }}
                          >
                            {feature.icon}
                            <Typography
                              variant="h6"
                              sx={{ ml: 1, fontWeight: 600 }}
                            >
                              {feature.title}
                            </Typography>
                          </Box>
                          <Typography
                            variant="body2"
                            sx={{
                              color: 'rgba(255, 255, 255, 0.8)',
                              lineHeight: 1.6,
                            }}
                          >
                            {feature.description}
                          </Typography>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default HeroSection;
