"""
USDC-DeFi Mining Platform - FastAPI Backend
Main application entry point
"""

import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent / "app"))

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
from contextlib import asynccontextmanager

# Import application modules
from app.core.config import settings
from app.core.database import engine, create_tables
from app.api.routes import api_router
from app.core.logging import setup_logging
from app.services.scheduler import start_scheduler, stop_scheduler

# Setup logging
logger = setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("🚀 Starting USDC-DeFi Mining Platform...")
    
    # Create database tables
    await create_tables()
    logger.info("✓ Database tables created/verified")
    
    # Start background scheduler for earnings distribution
    start_scheduler()
    logger.info("✓ Background scheduler started")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down USDC-DeFi Mining Platform...")
    stop_scheduler()
    logger.info("✓ Background scheduler stopped")

# Create FastAPI application
app = FastAPI(
    title="USDC-DeFi Mining Platform API",
    description="A decentralized finance mining platform for USDC deposits with tiered interest rates",
    version="1.0.0",
    docs_url="/api/docs" if settings.DEBUG else None,
    redoc_url="/api/redoc" if settings.DEBUG else None,
    openapi_url="/api/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Trusted host middleware for security
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring"""
    return {
        "status": "healthy",
        "service": "usdc-defi-api",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with basic information"""
    return {
        "message": "USDC-DeFi Mining Platform API",
        "version": "1.0.0",
        "docs": "/api/docs" if settings.DEBUG else "Documentation not available in production",
        "health": "/health"
    }

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    if settings.DEBUG:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "detail": str(exc),
                "type": type(exc).__name__
            }
        )
    else:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An unexpected error occurred. Please try again later."
            }
        )

# Static files (for serving frontend in production)
if settings.SERVE_STATIC:
    static_path = Path(__file__).parent.parent / "frontend" / "build"
    if static_path.exists():
        app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
        logger.info(f"✓ Static files mounted from {static_path}")

if __name__ == "__main__":
    # Development server configuration
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning",
        access_log=settings.DEBUG,
        workers=1 if settings.DEBUG else settings.WORKERS
    )
