# 前端测试指南

## 🚀 快速启动

### 1. 安装依赖
```bash
cd frontend
npm install
```

### 2. 启动开发服务器
```bash
npm start
```

应用将在 http://localhost:3000 启动

## 🧪 测试功能

### 基本功能测试

#### 1. 首页测试
- [ ] 访问 http://localhost:3000
- [ ] 检查Hero部分是否正确显示
- [ ] 检查实时活动滚动是否工作
- [ ] 检查利率层级表格是否显示
- [ ] 检查统计数据是否显示
- [ ] 检查FAQ部分是否可以展开/收起

#### 2. 导航测试
- [ ] 点击导航菜单各项是否正常跳转
- [ ] 移动端菜单是否正常工作
- [ ] 主题切换按钮是否工作

#### 3. 钱包连接测试
- [ ] 点击"Connect Wallet"按钮
- [ ] 检查RainbowKit模态框是否出现
- [ ] 尝试连接测试钱包（如果有）

#### 4. 页面路由测试
- [ ] 访问 /dashboard（需要钱包连接）
- [ ] 访问 /deposit（需要钱包连接）
- [ ] 访问 /whitepaper
- [ ] 访问 /audit
- [ ] 访问 /support
- [ ] 访问不存在的路径（应显示404页面）

### 响应式设计测试

#### 桌面端 (1200px+)
- [ ] 布局是否正确
- [ ] 导航菜单是否完整显示
- [ ] 卡片布局是否合理

#### 平板端 (768px - 1199px)
- [ ] 布局是否适配
- [ ] 导航是否正常

#### 移动端 (< 768px)
- [ ] 移动端菜单是否工作
- [ ] 布局是否适配
- [ ] 文字大小是否合适

### 性能测试

#### 加载性能
- [ ] 首页加载时间 < 3秒
- [ ] 图片是否正确加载
- [ ] 字体是否正确加载

#### 交互性能
- [ ] 页面切换是否流畅
- [ ] 动画是否流畅
- [ ] 滚动是否流畅

## 🐛 常见问题排查

### 依赖安装问题
```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm install
```

### 端口占用问题
```bash
# 使用不同端口启动
PORT=3001 npm start
```

### 钱包连接问题
- 确保浏览器安装了钱包扩展
- 检查网络设置
- 查看浏览器控制台错误信息

### 样式问题
- 检查CSS是否正确加载
- 确认Material-UI主题配置
- 检查响应式断点

## 📊 测试检查清单

### 功能测试
- [ ] 首页完整显示
- [ ] 导航菜单工作正常
- [ ] 钱包连接功能
- [ ] 页面路由正常
- [ ] 404页面显示
- [ ] 主题切换功能

### 界面测试
- [ ] 响应式设计
- [ ] 动画效果
- [ ] 加载状态
- [ ] 错误处理
- [ ] 无障碍访问

### 兼容性测试
- [ ] Chrome浏览器
- [ ] Firefox浏览器
- [ ] Safari浏览器
- [ ] Edge浏览器
- [ ] 移动端浏览器

## 🔧 开发工具

### 浏览器开发者工具
- **Elements**: 检查DOM结构和CSS
- **Console**: 查看JavaScript错误和日志
- **Network**: 检查网络请求
- **Performance**: 分析性能
- **Application**: 检查本地存储

### React开发者工具
安装React DevTools浏览器扩展来调试React组件

### 有用的快捷键
- `Ctrl+Shift+I` (Windows) / `Cmd+Option+I` (Mac): 打开开发者工具
- `Ctrl+Shift+M` (Windows) / `Cmd+Shift+M` (Mac): 切换设备模拟
- `F5` / `Ctrl+R`: 刷新页面
- `Ctrl+Shift+R`: 强制刷新（清除缓存）

## 📝 测试报告模板

### 测试环境
- 操作系统: 
- 浏览器: 
- 屏幕分辨率: 
- 测试时间: 

### 测试结果
- 通过的测试: 
- 失败的测试: 
- 发现的问题: 

### 问题详情
1. **问题描述**: 
   - 重现步骤: 
   - 预期结果: 
   - 实际结果: 
   - 截图: 

## 🎯 下一步

测试完成后，您可以：

1. **部署到测试环境**
   ```bash
   npm run build
   ```

2. **集成后端API**
   - 启动后端服务
   - 测试API调用
   - 验证数据流

3. **连接真实钱包**
   - 配置正确的网络
   - 测试真实交易
   - 验证智能合约交互

4. **性能优化**
   - 代码分割
   - 图片优化
   - 缓存策略

祝您测试顺利！🎉
