# USDC-DeFi Mining Platform

A decentralized finance mining platform where users can deposit USDC to earn liquidity mining rewards.

## Project Structure

```
defi/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── utils/          # Utility functions
│   │   ├── styles/         # CSS/SCSS files
│   │   └── config/         # Configuration files
│   ├── public/             # Static assets
│   └── package.json        # Frontend dependencies
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core functionality
│   │   ├── db/             # Database models and connections
│   │   ├── services/       # Business logic services
│   │   └── utils/          # Utility functions
│   ├── requirements.txt    # Python dependencies
│   └── main.py            # FastAPI application entry point
├── contracts/              # Smart contracts
│   ├── contracts/         # Solidity contracts
│   ├── scripts/           # Deployment scripts
│   └── hardhat.config.js  # Hardhat configuration
├── database/               # Database related files
│   ├── migrations/        # Database migration scripts
│   ├── seeds/             # Seed data including mock deposits
│   └── schema.sql         # Database schema
├── deployment/             # Deployment configurations
│   ├── nginx/             # Nginx configuration
│   ├── pm2/               # PM2 configuration
│   └── ssl/               # SSL certificate configs
└── docs/                   # Documentation
    ├── api/               # API documentation
    ├── deployment/        # Deployment guides
    └── user-guide/        # User manuals
```

## Technology Stack

- **Frontend**: React 18 + RainbowKit + Wagmi + Viem
- **Backend**: Python 3.8+ + FastAPI + SQLAlchemy
- **Database**: MySQL 8.0
- **Blockchain**: Ethereum + OpenZeppelin (Meta-transactions)
- **Deployment**: Baota Panel + Nginx + PM2
- **Wallet Integration**: RainbowKit with Project ID: 1a54ba92caa7810745990910f7daccc4

## Key Features

1. **Wallet Authentication**: Direct wallet connection login without invitation codes
2. **Tiered Interest Rates**: 9-tier interest rate structure (1.3% - 4.1% APY)
3. **Meta-transactions**: Gas-free transactions using OpenZeppelin framework
4. **Real-time Data**: Live deposit activity simulation with 30 mock entries
5. **Multi-language Support**: English interface with language switching
6. **Responsive Design**: Modern tech-style UI for desktop and mobile

## Interest Rate Tiers

| Deposit Range (USDC) | Annual Interest Rate |
|---------------------|---------------------|
| 100 - 4,999 | 1.3% - 1.6% |
| 5,000 - 19,999 | 1.6% - 1.9% |
| 20,000 - 49,999 | 1.9% - 2.2% |
| 50,000 - 99,999 | 2.2% - 2.5% |
| 100,000 - 199,999 | 2.5% - 2.8% |
| 200,000 - 499,999 | 2.8% - 3.1% |
| 500,000 - 999,999 | 3.1% - 3.5% |
| 1,000,000 - 1,999,999 | 3.5% - 3.8% |
| 2,000,000+ | 4.1% |

## Gas Fee Sponsor Wallet

**Address**: ******************************************

## Quick Start

1. Clone the repository
2. Install dependencies for frontend and backend
3. Set up MySQL 8.0 database
4. Configure environment variables
5. Deploy using Baota Panel

For detailed deployment instructions, see `docs/deployment/baota-setup.md`

## License

MIT License
