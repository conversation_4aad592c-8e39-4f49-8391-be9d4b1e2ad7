import React from 'react';
import { Box, Container, Typography, Grid } from '@mui/material';
import CountUp from 'react-countup';

const StatsSection: React.FC = () => {
  const stats = [
    { label: 'Total Value Locked', value: 15750000, prefix: '$' },
    { label: 'Active Users', value: 1250, suffix: '+' },
    { label: 'Total Earnings Paid', value: 425000, prefix: '$' },
    { label: 'Platform Uptime', value: 99.9, suffix: '%' },
  ];

  return (
    <Box sx={{ py: 8, backgroundColor: 'primary.main', color: 'white' }}>
      <Container maxWidth="lg">
        <Typography variant="h3" textAlign="center" gutterBottom sx={{ mb: 6 }}>
          Platform Statistics
        </Typography>
        <Grid container spacing={4}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Box textAlign="center">
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                  {stat.prefix}
                  <CountUp end={stat.value} duration={2} separator="," />
                  {stat.suffix}
                </Typography>
                <Typography variant="h6">{stat.label}</Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default StatsSection;
