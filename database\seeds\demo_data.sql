-- Demo data for development and testing
USE usdc_defi;

-- Insert test users (for development only)
INSERT INTO users (wallet_address, total_deposited, total_earned, last_login_at) VALUES
('******************************************', 50000.000000, 1250.000000, NOW() - INTERVAL 1 HOUR),
('0x8ba1f109551bD432803012645Hac189451c4a89', 125000.000000, 3500.000000, NOW() - INTERVAL 2 HOUR),
('******************************************', 25000.000000, 580.000000, NOW() - INTERVAL 3 HOUR),
('******************************************', 750000.000000, 22500.000000, NOW() - INTERVAL 4 HOUR),
('******************************************', 15000.000000, 285.000000, NOW() - INTERVAL 5 HOUR);

-- Insert test deposits
INSERT INTO deposits (user_id, amount, interest_tier_id, interest_rate, transaction_hash, status) VALUES
(1, 50000.000000, 4, 0.0235, '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef', 'confirmed'),
(2, 125000.000000, 5, 0.0265, '0x2345678901bcdef12345678901bcdef12345678901bcdef12345678901bcdef1', 'confirmed'),
(3, 25000.000000, 3, 0.0205, '0x3456789012cdef123456789012cdef123456789012cdef123456789012cdef12', 'confirmed'),
(4, 750000.000000, 7, 0.0330, '0x456789013def1234567890123def1234567890123def1234567890123def123', 'confirmed'),
(5, 15000.000000, 2, 0.0175, '0x56789014ef123456789014ef123456789014ef123456789014ef123456789', 'confirmed');

-- Insert test earnings (last 7 days)
INSERT INTO earnings (user_id, deposit_id, amount, earning_date, interest_rate, status, transaction_hash) VALUES
-- User 1 earnings
(1, 1, 3.424658, CURDATE() - INTERVAL 1 DAY, 0.0235, 'distributed', '0x7890123456789012345678901234567890123456789012345678901234567890'),
(1, 1, 3.424658, CURDATE() - INTERVAL 2 DAY, 0.0235, 'distributed', '0x8901234567890123456789012345678901234567890123456789012345678901'),
(1, 1, 3.424658, CURDATE() - INTERVAL 3 DAY, 0.0235, 'distributed', '0x9012345678901234567890123456789012345678901234567890123456789012'),
-- User 2 earnings
(2, 2, 9.041096, CURDATE() - INTERVAL 1 DAY, 0.0265, 'distributed', '0xa123456789012345678901234567890123456789012345678901234567890123'),
(2, 2, 9.041096, CURDATE() - INTERVAL 2 DAY, 0.0265, 'distributed', '0xb234567890123456789012345678901234567890123456789012345678901234'),
(2, 2, 9.041096, CURDATE() - INTERVAL 3 DAY, 0.0265, 'distributed', '0xc345678901234567890123456789012345678901234567890123456789012345'),
-- User 3 earnings
(3, 3, 1.404110, CURDATE() - INTERVAL 1 DAY, 0.0205, 'distributed', '0xd456789012345678901234567890123456789012345678901234567890123456'),
(3, 3, 1.404110, CURDATE() - INTERVAL 2 DAY, 0.0205, 'distributed', '0xe567890123456789012345678901234567890123456789012345678901234567'),
(3, 3, 1.404110, CURDATE() - INTERVAL 3 DAY, 0.0205, 'distributed', '0xf678901234567890123456789012345678901234567890123456789012345678'),
-- User 4 earnings
(4, 4, 67.808219, CURDATE() - INTERVAL 1 DAY, 0.0330, 'distributed', '0x1789012345678901234567890123456789012345678901234567890123456789'),
(4, 4, 67.808219, CURDATE() - INTERVAL 2 DAY, 0.0330, 'distributed', '0x2890123456789012345678901234567890123456789012345678901234567890'),
(4, 4, 67.808219, CURDATE() - INTERVAL 3 DAY, 0.0330, 'distributed', '0x3901234567890123456789012345678901234567890123456789012345678901'),
-- User 5 earnings
(5, 5, 0.719178, CURDATE() - INTERVAL 1 DAY, 0.0175, 'distributed', '0x4012345678901234567890123456789012345678901234567890123456789012'),
(5, 5, 0.719178, CURDATE() - INTERVAL 2 DAY, 0.0175, 'distributed', '0x5123456789012345678901234567890123456789012345678901234567890123'),
(5, 5, 0.719178, CURDATE() - INTERVAL 3 DAY, 0.0175, 'distributed', '0x6234567890123456789012345678901234567890123456789012345678901234');

-- Insert test transactions
INSERT INTO transactions (user_id, transaction_hash, transaction_type, amount, gas_fee, block_number, block_timestamp, status) VALUES
(1, '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef', 'deposit', 50000.000000, 0.000000, 18500000, NOW() - INTERVAL 7 DAY, 'confirmed'),
(2, '0x2345678901bcdef12345678901bcdef12345678901bcdef12345678901bcdef1', 'deposit', 125000.000000, 0.000000, 18500100, NOW() - INTERVAL 6 DAY, 'confirmed'),
(3, '0x3456789012cdef123456789012cdef123456789012cdef123456789012cdef12', 'deposit', 25000.000000, 0.000000, 18500200, NOW() - INTERVAL 5 DAY, 'confirmed'),
(4, '0x456789013def1234567890123def1234567890123def1234567890123def123', 'deposit', 750000.000000, 0.000000, 18500300, NOW() - INTERVAL 4 DAY, 'confirmed'),
(5, '0x56789014ef123456789014ef123456789014ef123456789014ef123456789', 'deposit', 15000.000000, 0.000000, 18500400, NOW() - INTERVAL 3 DAY, 'confirmed');

-- Update demo_deposits with more realistic timestamps
UPDATE demo_deposits SET 
    display_time = CASE 
        WHEN id <= 5 THEN CONCAT(FLOOR(RAND() * 10) + 1, ' minutes ago')
        WHEN id <= 10 THEN CONCAT(FLOOR(RAND() * 30) + 10, ' minutes ago')
        WHEN id <= 20 THEN CONCAT(FLOOR(RAND() * 60) + 30, ' minutes ago')
        ELSE CONCAT(FLOOR(RAND() * 120) + 60, ' minutes ago')
    END,
    created_at = NOW() - INTERVAL FLOOR(RAND() * 120) MINUTE;
