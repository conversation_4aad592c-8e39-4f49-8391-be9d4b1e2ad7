import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';

// Wagmi and RainbowKit imports
import { WagmiConfig } from 'wagmi';
import { RainbowKitProvider, darkTheme } from '@rainbow-me/rainbowkit';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Application imports
import App from './App';
import { wagmiConfig, chains } from './config/wagmi';
import { ThemeProvider } from './config/theme';
import { AuthProvider } from './hooks/useAuth';
import { NotificationProvider } from './hooks/useNotifications';

// Styles
import './styles/index.css';
import '@rainbow-me/rainbowkit/styles.css';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

// RainbowKit theme configuration
const rainbowKitTheme = darkTheme({
  accentColor: '#0066ff',
  accentColorForeground: 'white',
  borderRadius: 'medium',
  fontStack: 'system',
  overlayBlur: 'small',
});

// Toast configuration
const toastOptions = {
  duration: 4000,
  position: 'top-right' as const,
  style: {
    background: '#1a1a1a',
    color: '#ffffff',
    border: '1px solid #333',
    borderRadius: '8px',
  },
  success: {
    iconTheme: {
      primary: '#10b981',
      secondary: '#ffffff',
    },
  },
  error: {
    iconTheme: {
      primary: '#ef4444',
      secondary: '#ffffff',
    },
  },
};

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <HelmetProvider>
      <BrowserRouter>
        <QueryClientProvider client={queryClient}>
          <WagmiConfig config={wagmiConfig}>
            <RainbowKitProvider
              chains={chains}
              theme={rainbowKitTheme}
              appInfo={{
                appName: process.env.REACT_APP_APP_NAME || 'USDC-DeFi Mining Platform',
                learnMoreUrl: process.env.REACT_APP_DOCS_URL || 'https://docs.usdcdefi.com',
              }}
              modalSize="compact"
              coolMode
            >
              <ThemeProvider>
                <AuthProvider>
                  <NotificationProvider>
                    <App />
                    <Toaster toastOptions={toastOptions} />
                  </NotificationProvider>
                </AuthProvider>
              </ThemeProvider>
            </RainbowKitProvider>
          </WagmiConfig>
        </QueryClientProvider>
      </BrowserRouter>
    </HelmetProvider>
  </React.StrictMode>
);
