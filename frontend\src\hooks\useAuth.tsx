import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAccount, useSignMessage } from 'wagmi';
import { api, TokenManager } from '../utils/api';
import toast from 'react-hot-toast';

// Types
interface User {
  id: number;
  wallet_address: string;
  created_at: string;
  updated_at: string;
  last_login_at: string | null;
  is_active: boolean;
  total_deposited: string;
  total_earned: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

// Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider Props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth Provider Component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { address, isConnected } = useAccount();
  const { signMessageAsync } = useSignMessage();

  // Check if user is authenticated
  const isAuthenticated = !!user && !!address && isConnected;

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      const token = TokenManager.getToken();
      
      if (token && !TokenManager.isTokenExpired(token) && isConnected && address) {
        try {
          await refreshUser();
        } catch (error) {
          console.error('Failed to refresh user:', error);
          TokenManager.clearTokens();
        }
      }
      
      setIsLoading(false);
    };

    initAuth();
  }, [isConnected, address]);

  // Auto logout when wallet disconnects
  useEffect(() => {
    if (!isConnected && user) {
      logout();
    }
  }, [isConnected, user]);

  // Login function
  const login = async (): Promise<void> => {
    if (!address || !isConnected) {
      toast.error('Please connect your wallet first');
      return;
    }

    try {
      setIsLoading(true);

      // Step 1: Generate authentication message
      const messageResponse = await api.auth.generateMessage(address);
      const { message, timestamp } = messageResponse.data.data;

      // Step 2: Sign the message
      const signature = await signMessageAsync({ message });

      // Step 3: Login with signature
      const loginResponse = await api.auth.walletLogin({
        wallet_address: address,
        signature,
        message,
        timestamp,
      });

      // Step 4: Store token and user data
      const { access_token, user: userData } = loginResponse.data;
      TokenManager.setToken(access_token);
      setUser(userData);

      toast.success('Successfully logged in!');
    } catch (error: any) {
      console.error('Login failed:', error);
      
      if (error.code === 4001) {
        toast.error('Signature rejected by user');
      } else if (error.response?.status === 401) {
        toast.error('Invalid signature. Please try again.');
      } else {
        toast.error('Login failed. Please try again.');
      }
      
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = (): void => {
    try {
      // Call logout API (optional, since JWT is stateless)
      api.auth.logout().catch(console.error);
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      // Clear local state regardless of API call result
      TokenManager.clearTokens();
      setUser(null);
      toast.success('Logged out successfully');
    }
  };

  // Refresh user data
  const refreshUser = async (): Promise<void> => {
    try {
      const response = await api.auth.getCurrentUser();
      setUser(response.data);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      throw error;
    }
  };

  // Context value
  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook for protected routes
export const useRequireAuth = (): AuthContextType => {
  const auth = useAuth();
  
  useEffect(() => {
    if (!auth.isLoading && !auth.isAuthenticated) {
      toast.error('Please connect your wallet to access this page');
    }
  }, [auth.isLoading, auth.isAuthenticated]);

  return auth;
};

// Higher-order component for protected routes
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => {
    const { isAuthenticated, isLoading } = useRequireAuth();

    if (isLoading) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '200px' 
        }}>
          <div className="loading-spinner" />
        </div>
      );
    }

    if (!isAuthenticated) {
      return (
        <div style={{ 
          textAlign: 'center', 
          padding: '2rem',
          color: '#666'
        }}>
          <h3>Authentication Required</h3>
          <p>Please connect your wallet to access this page.</p>
        </div>
      );
    }

    return <Component {...props} />;
  };
};
