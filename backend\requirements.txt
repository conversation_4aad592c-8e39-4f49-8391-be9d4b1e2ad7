# FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Database dependencies
sqlalchemy==2.0.23
mysql-connector-python==8.2.0
alembic==1.12.1

# Blockchain and Web3 dependencies
web3==6.11.3
eth-account==0.9.0
eth-utils==2.3.0
hexbytes==0.3.1

# Cryptography and security
cryptography==41.0.7
pycryptodome==3.19.0
python-dotenv==1.0.0

# HTTP and API dependencies
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.1

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Date and time handling
python-dateutil==2.8.2
pytz==2023.3

# Background tasks and scheduling
celery==5.3.4
redis==5.0.1
apscheduler==3.10.4

# Logging and monitoring
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0

# CORS and middleware
fastapi-cors==0.0.6

# JSON and data processing
orjson==3.9.10
pandas==2.1.3
numpy==1.25.2

# Environment and configuration
python-decouple==3.8

# Rate limiting
slowapi==0.1.9

# WebSocket support
websockets==12.0

# File handling
aiofiles==23.2.1

# Decimal handling for financial calculations
decimal==1.70

# OpenZeppelin integration utilities
eth-abi==4.2.1
eth-typing==3.5.2
