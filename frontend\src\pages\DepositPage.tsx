import React, { useState } from 'react';
import {
  Container,
  <PERSON><PERSON><PERSON>,
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
} from '@mui/material';
import { useAuth } from '../hooks/useAuth';

const DepositPage: React.FC = () => {
  const { user } = useAuth();
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);

  const handleDeposit = async () => {
    if (!amount || parseFloat(amount) < 100) {
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement deposit logic
      console.log('Depositing:', amount);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('Deposit successful!');
      setAmount('');
    } catch (error) {
      console.error('Deposit failed:', error);
      alert('Deposit failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getInterestRate = (depositAmount: number) => {
    if (depositAmount >= 2000000) return '4.1%';
    if (depositAmount >= 1000000) return '3.5% - 3.8%';
    if (depositAmount >= 500000) return '3.1% - 3.5%';
    if (depositAmount >= 200000) return '2.8% - 3.1%';
    if (depositAmount >= 100000) return '2.5% - 2.8%';
    if (depositAmount >= 50000) return '2.2% - 2.5%';
    if (depositAmount >= 20000) return '1.9% - 2.2%';
    if (depositAmount >= 5000) return '1.6% - 1.9%';
    if (depositAmount >= 100) return '1.3% - 1.6%';
    return 'Below minimum';
  };

  const currentAmount = parseFloat(amount) || 0;
  const interestRate = getInterestRate(currentAmount);

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Make a Deposit
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Deposit USDC to start earning competitive yields with our tiered interest rate system.
      </Typography>

      <Card>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Deposit Amount
            </Typography>
            <TextField
              fullWidth
              label="Amount (USDC)"
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="Enter amount (minimum 100 USDC)"
              inputProps={{ min: 100, step: 1 }}
            />
          </Box>

          {currentAmount >= 100 && (
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                <strong>Interest Rate:</strong> {interestRate} APY
                <br />
                <strong>Estimated Annual Earnings:</strong> ${(currentAmount * 0.025).toLocaleString()} USDC
              </Typography>
            </Alert>
          )}

          {currentAmount > 0 && currentAmount < 100 && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              Minimum deposit amount is 100 USDC
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              size="large"
              onClick={handleDeposit}
              disabled={!amount || currentAmount < 100 || loading}
              sx={{ flex: 1 }}
            >
              {loading ? 'Processing...' : 'Deposit USDC'}
            </Button>
            <Button
              variant="outlined"
              size="large"
              onClick={() => setAmount('')}
              disabled={loading}
            >
              Clear
            </Button>
          </Box>

          <Alert severity="info" sx={{ mt: 3 }}>
            <Typography variant="body2">
              <strong>Gas-Free:</strong> All transaction fees are sponsored by the platform.
              You won't pay any gas fees for this deposit.
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    </Container>
  );
};

export default DepositPage;
