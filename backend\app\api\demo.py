"""
Demo data API routes for homepage scrolling display
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.core.database import get_db
from app.db.schemas import DemoDepositResponse, APIResponse
from app.services.demo import DemoService

router = APIRouter()

@router.get("/deposits", response_model=List[DemoDepositResponse], summary="Get demo deposits")
async def get_demo_deposits(
    limit: int = Query(default=30, ge=1, le=100, description="Number of demo deposits to return"),
    active_only: bool = Query(default=True, description="Return only active demo deposits"),
    db: Session = Depends(get_db)
):
    """
    Get demo deposit data for homepage scrolling display
    
    Returns a list of mock deposit records that simulate real platform activity.
    This data is used for the homepage scrolling animation to show platform activity.
    """
    try:
        demo_service = DemoService(db)
        demo_deposits = await demo_service.get_demo_deposits(
            limit=limit,
            active_only=active_only
        )
        
        return [DemoDepositResponse.from_orm(deposit) for deposit in demo_deposits]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch demo deposits: {str(e)}"
        )

@router.get("/deposits/random", response_model=List[DemoDepositResponse], summary="Get random demo deposits")
async def get_random_demo_deposits(
    count: int = Query(default=10, ge=1, le=50, description="Number of random deposits to return"),
    db: Session = Depends(get_db)
):
    """
    Get random demo deposits for varied display
    
    Returns a randomized selection of demo deposits to create more dynamic
    scrolling content on the homepage.
    """
    try:
        demo_service = DemoService(db)
        demo_deposits = await demo_service.get_random_demo_deposits(count=count)
        
        return [DemoDepositResponse.from_orm(deposit) for deposit in demo_deposits]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch random demo deposits: {str(e)}"
        )

@router.get("/deposits/recent", response_model=List[DemoDepositResponse], summary="Get recent demo deposits")
async def get_recent_demo_deposits(
    minutes: int = Query(default=60, ge=1, le=1440, description="Get deposits from last N minutes"),
    limit: int = Query(default=20, ge=1, le=100, description="Maximum number of deposits to return"),
    db: Session = Depends(get_db)
):
    """
    Get recent demo deposits based on display time
    
    Returns demo deposits that appear to be from the last N minutes,
    useful for showing "recent activity" on the platform.
    """
    try:
        demo_service = DemoService(db)
        demo_deposits = await demo_service.get_recent_demo_deposits(
            minutes=minutes,
            limit=limit
        )
        
        return [DemoDepositResponse.from_orm(deposit) for deposit in demo_deposits]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch recent demo deposits: {str(e)}"
        )

@router.get("/stats", response_model=dict, summary="Get demo data statistics")
async def get_demo_stats(db: Session = Depends(get_db)):
    """
    Get statistics about demo deposit data
    
    Returns summary statistics about the demo data including total count,
    amount ranges, and activity distribution.
    """
    try:
        demo_service = DemoService(db)
        stats = await demo_service.get_demo_stats()
        
        return {
            "success": True,
            "message": "Demo statistics retrieved successfully",
            "data": stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch demo statistics: {str(e)}"
        )

@router.post("/refresh", response_model=APIResponse, summary="Refresh demo data timestamps")
async def refresh_demo_data(db: Session = Depends(get_db)):
    """
    Refresh demo data with new timestamps
    
    Updates the display_time fields of demo deposits to create fresh
    "recent activity" for the homepage display.
    """
    try:
        demo_service = DemoService(db)
        updated_count = await demo_service.refresh_demo_timestamps()
        
        return APIResponse(
            success=True,
            message=f"Demo data refreshed successfully. Updated {updated_count} records.",
            data={"updated_count": updated_count}
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to refresh demo data: {str(e)}"
        )

@router.get("/deposits/by-amount", response_model=List[DemoDepositResponse], summary="Get demo deposits by amount range")
async def get_demo_deposits_by_amount(
    min_amount: Optional[float] = Query(default=None, ge=0, description="Minimum deposit amount"),
    max_amount: Optional[float] = Query(default=None, ge=0, description="Maximum deposit amount"),
    limit: int = Query(default=20, ge=1, le=100, description="Maximum number of deposits to return"),
    db: Session = Depends(get_db)
):
    """
    Get demo deposits filtered by amount range
    
    Returns demo deposits within a specific amount range, useful for
    showcasing different tier activities.
    """
    try:
        demo_service = DemoService(db)
        demo_deposits = await demo_service.get_demo_deposits_by_amount(
            min_amount=min_amount,
            max_amount=max_amount,
            limit=limit
        )
        
        return [DemoDepositResponse.from_orm(deposit) for deposit in demo_deposits]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch demo deposits by amount: {str(e)}"
        )

@router.get("/activity-feed", response_model=dict, summary="Get formatted activity feed")
async def get_activity_feed(
    count: int = Query(default=15, ge=1, le=50, description="Number of activities to return"),
    db: Session = Depends(get_db)
):
    """
    Get formatted activity feed for homepage display
    
    Returns a formatted list of demo activities ready for frontend display,
    including formatted amounts, anonymized addresses, and time strings.
    """
    try:
        demo_service = DemoService(db)
        activity_feed = await demo_service.get_formatted_activity_feed(count=count)
        
        return {
            "success": True,
            "message": "Activity feed retrieved successfully",
            "data": {
                "activities": activity_feed,
                "count": len(activity_feed),
                "last_updated": demo_service.get_last_update_time()
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch activity feed: {str(e)}"
        )
