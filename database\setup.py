#!/usr/bin/env python3
"""
Database setup script for USDC-DeFi Mining Platform
This script creates the database, tables, and inserts initial data
"""

import mysql.connector
import os
import sys
from pathlib import Path

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',  # Change this for production
    'password': '',  # Set your MySQL root password
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}

def read_sql_file(file_path):
    """Read SQL file and return its content"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print(f"Error: SQL file not found: {file_path}")
        return None
    except Exception as e:
        print(f"Error reading SQL file {file_path}: {e}")
        return None

def execute_sql_script(cursor, sql_script, script_name):
    """Execute SQL script with proper error handling"""
    try:
        # Split the script into individual statements
        statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]
        
        for statement in statements:
            if statement:
                cursor.execute(statement)
        
        print(f"✓ Successfully executed {script_name}")
        return True
    except mysql.connector.Error as e:
        print(f"✗ Error executing {script_name}: {e}")
        return False

def setup_database():
    """Main database setup function"""
    print("🚀 Starting USDC-DeFi Database Setup...")
    
    # Get the directory of this script
    script_dir = Path(__file__).parent
    
    try:
        # Connect to MySQL server (without specifying database)
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("✓ Connected to MySQL server")
        
        # 1. Create database and tables
        schema_file = script_dir / 'schema.sql'
        schema_sql = read_sql_file(schema_file)
        if schema_sql:
            if execute_sql_script(cursor, schema_sql, "Database Schema"):
                connection.commit()
            else:
                return False
        
        # 2. Insert initial data
        initial_data_file = script_dir / 'migrations' / '001_initial_data.sql'
        initial_data_sql = read_sql_file(initial_data_file)
        if initial_data_sql:
            if execute_sql_script(cursor, initial_data_sql, "Initial Data"):
                connection.commit()
            else:
                return False
        
        # 3. Insert demo data (optional, for development)
        demo_data_file = script_dir / 'seeds' / 'demo_data.sql'
        demo_data_sql = read_sql_file(demo_data_file)
        if demo_data_sql:
            print("\n📝 Do you want to insert demo data for development? (y/n): ", end="")
            choice = input().lower().strip()
            if choice in ['y', 'yes']:
                if execute_sql_script(cursor, demo_data_sql, "Demo Data"):
                    connection.commit()
                    print("✓ Demo data inserted successfully")
                else:
                    print("⚠️  Demo data insertion failed, but continuing...")
            else:
                print("⏭️  Skipping demo data insertion")
        
        print("\n🎉 Database setup completed successfully!")
        print("\nDatabase Information:")
        print(f"  - Database Name: usdc_defi")
        print(f"  - Host: {DB_CONFIG['host']}")
        print(f"  - Port: {DB_CONFIG['port']}")
        print(f"  - Character Set: utf8mb4")
        
        # Display table information
        cursor.execute("USE usdc_defi")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"\nCreated Tables ({len(tables)}):")
        for table in tables:
            print(f"  - {table[0]}")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"✗ Database connection error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
        print("\n🔌 Database connection closed")

def verify_setup():
    """Verify that the database setup was successful"""
    try:
        # Connect to the usdc_defi database
        config = DB_CONFIG.copy()
        config['database'] = 'usdc_defi'
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("\n🔍 Verifying database setup...")
        
        # Check interest tiers
        cursor.execute("SELECT COUNT(*) FROM interest_tiers")
        tier_count = cursor.fetchone()[0]
        print(f"✓ Interest tiers: {tier_count} records")
        
        # Check demo deposits
        cursor.execute("SELECT COUNT(*) FROM demo_deposits")
        demo_count = cursor.fetchone()[0]
        print(f"✓ Demo deposits: {demo_count} records")
        
        # Check system config
        cursor.execute("SELECT COUNT(*) FROM system_config")
        config_count = cursor.fetchone()[0]
        print(f"✓ System config: {config_count} records")
        
        print("✅ Database verification completed successfully!")
        
    except mysql.connector.Error as e:
        print(f"✗ Verification failed: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    print("=" * 60)
    print("  USDC-DeFi Mining Platform - Database Setup")
    print("=" * 60)
    
    # Check if MySQL connector is available
    try:
        import mysql.connector
    except ImportError:
        print("✗ Error: mysql-connector-python is not installed")
        print("Please install it using: pip install mysql-connector-python")
        sys.exit(1)
    
    # Setup database
    if setup_database():
        verify_setup()
        print("\n🎯 Next Steps:")
        print("1. Update database credentials in backend configuration")
        print("2. Start the FastAPI backend server")
        print("3. Configure frontend environment variables")
        print("4. Deploy to Baota panel")
    else:
        print("\n❌ Database setup failed. Please check the errors above.")
        sys.exit(1)
